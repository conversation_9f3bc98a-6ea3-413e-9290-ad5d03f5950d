package com.danding.mall.app.base.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * OSS配置类
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssConfig {

    /**
     * 阿里云AccessKey ID
     */
    private String accessKeyId;

    /**
     * 阿里云AccessKey Secret
     */
    private String accessKeySecret;

    /**
     * STS角色ARN
     */
    private String roleArn;

    /**
     * OSS Bucket名称
     */
    private String bucket;

    /**
     * OSS区域
     */
    private String region = "cn-hangzhou";

    /**
     * OSS域名
     */
    private String host;

    /**
     * 上传目录前缀
     */
    private String uploadDir = "uploads";

    /**
     * STS临时凭证过期时间（秒）
     */
    private Long expireTime = 3600L;

    /**
     * 文件大小限制（字节）
     */
    private Long maxFileSize = 10485760L; // 10MB

    /**
     * STS端点
     */
    private String stsEndpoint = "sts.cn-hangzhou.aliyuncs.com";

    /**
     * 角色会话名称
     */
    private String roleSessionName = "mall-app-session";
}
