package com.danding.mall.app.base.controller;

import com.danding.core.client.Result;
import com.danding.mall.app.base.utils.OssService;
import com.danding.mall.app.base.vo.OssPostSignatureVO;
import com.danding.mall.app.base.vo.OssStsTokenVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * OSS控制器基类
 * 提供OSS相关的通用接口
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseOssController {

    @Autowired
    protected OssService ossService;

    /**
     * 获取STS临时凭证
     * 用于客户端直接上传到OSS
     * 
     * @return STS临时凭证信息
     */
    @GetMapping("/oss/sts-token")
    public Result<OssStsTokenVO> getStsToken() {
        try {
            log.info("收到获取OSS STS临时凭证请求");
            OssStsTokenVO tokenVO = ossService.getStsToken();
            return Result.success(tokenVO);
        } catch (Exception e) {
            log.error("获取OSS STS临时凭证失败", e);
            return Result.fail("获取OSS STS临时凭证失败: " + e.getMessage());
        }
    }

    /**
     * 获取PostObject上传签名
     * 用于Web端表单上传
     * 
     * @return PostObject签名信息
     */
    @GetMapping("/oss/post-signature")
    public Result<OssPostSignatureVO> getPostSignature() {
        try {
            log.info("收到获取OSS PostObject签名请求");
            OssPostSignatureVO signatureVO = ossService.getPostSignature();
            return Result.success(signatureVO);
        } catch (Exception e) {
            log.error("获取OSS PostObject签名失败", e);
            return Result.fail("获取OSS PostObject签名失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一文件名
     * 
     * @param originalFilename 原始文件名
     * @param subDir 子目录（可选）
     * @return 生成的文件路径
     */
    @GetMapping("/oss/generate-filename")
    public Result<String> generateFileName(
            @RequestParam("originalFilename") String originalFilename,
            @RequestParam(value = "subDir", required = false) String subDir) {
        try {
            log.info("收到生成文件名请求，原始文件名: {}, 子目录: {}", originalFilename, subDir);
            
            // 验证文件类型（可根据需要调整允许的文件类型）
            String[] allowedExtensions = {"jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"};
            if (!ossService.isFileTypeAllowed(originalFilename, allowedExtensions)) {
                return Result.fail("不支持的文件类型");
            }
            
            String uniqueFileName = ossService.generateUniqueFileName(originalFilename);
            String filePath = ossService.generateFilePath(uniqueFileName, subDir);
            
            log.info("生成文件路径: {}", filePath);
            return Result.success(filePath);
        } catch (Exception e) {
            log.error("生成文件名失败", e);
            return Result.fail("生成文件名失败: " + e.getMessage());
        }
    }
}
