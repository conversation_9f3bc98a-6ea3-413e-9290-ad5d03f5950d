package com.danding.mall.app.base.utils;

import com.danding.mall.app.base.vo.OssPostSignatureVO;
import com.danding.mall.app.base.vo.OssStsTokenVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * OSS服务类
 * 提供OSS相关的业务服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OssService {

    @Autowired
    private OssUtil ossUtil;

    /**
     * 获取STS临时凭证
     * 用于客户端直接上传到OSS
     * 
     * @return STS临时凭证信息
     */
    public OssStsTokenVO getStsToken() {
        try {
            log.info("开始获取OSS STS临时凭证");
            OssStsTokenVO tokenVO = ossUtil.getStsToken();
            log.info("成功获取OSS STS临时凭证，bucket: {}", tokenVO.getBucket());
            return tokenVO;
        } catch (Exception e) {
            log.error("获取OSS STS临时凭证失败", e);
            throw new RuntimeException("获取OSS STS临时凭证失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取PostObject上传签名
     * 用于Web端表单上传
     * 
     * @return PostObject签名信息
     */
    public OssPostSignatureVO getPostSignature() {
        try {
            log.info("开始生成OSS PostObject签名");
            OssPostSignatureVO signatureVO = ossUtil.getPostSignature();
            log.info("成功生成OSS PostObject签名，bucket: {}", signatureVO.getBucket());
            return signatureVO;
        } catch (Exception e) {
            log.error("生成OSS PostObject签名失败", e);
            throw new RuntimeException("生成OSS PostObject签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成唯一的文件名
     * 
     * @param originalFilename 原始文件名
     * @return 唯一的文件名
     */
    public String generateUniqueFileName(String originalFilename) {
        if (originalFilename == null || originalFilename.isEmpty()) {
            return System.currentTimeMillis() + "";
        }
        
        String extension = "";
        int lastDotIndex = originalFilename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = originalFilename.substring(lastDotIndex);
        }
        
        return System.currentTimeMillis() + "_" + RandomUtils.generateRandomString(8) + extension;
    }

    /**
     * 生成文件的完整路径
     * 
     * @param fileName 文件名
     * @param subDir 子目录（可选）
     * @return 完整的文件路径
     */
    public String generateFilePath(String fileName, String subDir) {
        StringBuilder pathBuilder = new StringBuilder();
        
        // 添加配置的上传目录前缀
        pathBuilder.append(ossUtil.getOssConfig().getUploadDir());
        
        // 添加日期目录
        pathBuilder.append("/").append(java.time.LocalDate.now().toString());
        
        // 添加子目录（如果有）
        if (subDir != null && !subDir.isEmpty()) {
            pathBuilder.append("/").append(subDir);
        }
        
        // 添加文件名
        pathBuilder.append("/").append(fileName);
        
        return pathBuilder.toString();
    }

    /**
     * 验证文件类型是否允许
     * 
     * @param fileName 文件名
     * @param allowedExtensions 允许的文件扩展名数组
     * @return 是否允许
     */
    public boolean isFileTypeAllowed(String fileName, String[] allowedExtensions) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex <= 0) {
            return false;
        }
        
        String extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        
        for (String allowedExt : allowedExtensions) {
            if (allowedExt.toLowerCase().equals(extension)) {
                return true;
            }
        }
        
        return false;
    }
}
