package com.danding.mall.app.base.utils;

import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.sts20150401.Client;
import com.aliyun.sts20150401.models.AssumeRoleRequest;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.danding.mall.app.base.config.OssConfig;
import com.danding.mall.app.base.vo.OssPostSignatureVO;
import com.danding.mall.app.base.vo.OssStsTokenVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * OSS工具类
 * 提供STS临时凭证获取和PostObject签名生成功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OssUtil {

    @Autowired
    private OssConfig ossConfig;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 获取OSS配置
     *
     * @return OSS配置
     */
    public OssConfig getOssConfig() {
        return ossConfig;
    }

    /**
     * 获取STS临时凭证
     *
     * @return STS临时凭证信息
     * @throws Exception 获取失败时抛出异常
     */
    public OssStsTokenVO getStsToken() throws Exception {
        log.info("开始获取OSS STS临时凭证");

        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials credentials = getCredentials();

        OssStsTokenVO tokenVO = new OssStsTokenVO();
        tokenVO.setAccessKeyId(credentials.accessKeyId);
        tokenVO.setAccessKeySecret(credentials.accessKeySecret);
        tokenVO.setSecurityToken(credentials.securityToken);
        tokenVO.setExpiration(credentials.expiration);
        tokenVO.setBucket(ossConfig.getBucket());
        tokenVO.setRegion(ossConfig.getRegion());
        tokenVO.setHost(ossConfig.getHost());
        tokenVO.setUploadDir(ossConfig.getUploadDir());
        tokenVO.setMaxFileSize(ossConfig.getMaxFileSize());

        log.info("成功获取OSS STS临时凭证，过期时间: {}", credentials.expiration);
        return tokenVO;
    }

    /**
     * 获取PostObject上传签名
     *
     * @return PostObject签名信息
     * @throws Exception 生成签名失败时抛出异常
     */
    public OssPostSignatureVO getPostSignature() throws Exception {
        log.info("开始生成OSS PostObject签名");

        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials credentials = getCredentials();

        String accessKeyId = credentials.accessKeyId;
        String accessKeySecret = credentials.accessKeySecret;
        String securityToken = credentials.securityToken;

        // 获取当前日期，格式为yyyyMMdd
        LocalDate today = LocalDate.now();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String date = today.format(dateFormatter);

        // 获取x-oss-date
        ZonedDateTime now = ZonedDateTime.now().withZoneSameInstant(ZoneOffset.UTC);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
        String xOssDate = now.format(dateTimeFormatter);

        // 创建x-oss-credential
        String xOssCredential = accessKeyId + "/" + date + "/" + ossConfig.getRegion() + "/oss/aliyun_v4_request";

        // 步骤1：创建policy
        Map<String, Object> policy = createPolicy(securityToken, xOssCredential, xOssDate);
        String jsonPolicy = OBJECT_MAPPER.writeValueAsString(policy);

        // 步骤2：构造待签名字符串（StringToSign）
        String stringToSign = new String(Base64.encodeBase64(jsonPolicy.getBytes()));

        // 步骤3：计算SigningKey
        byte[] signingKey = calculateSigningKey(accessKeySecret, date);

        // 步骤4：计算Signature
        byte[] signatureBytes = hmacSha256(signingKey, stringToSign);
        String signature = BinaryUtil.toHex(signatureBytes);

        // 构造响应
        OssPostSignatureVO signatureVO = new OssPostSignatureVO();
        signatureVO.setVersion("OSS4-HMAC-SHA256");
        signatureVO.setPolicy(stringToSign);
        signatureVO.setXOssCredential(xOssCredential);
        signatureVO.setXOssDate(xOssDate);
        signatureVO.setSignature(signature);
        signatureVO.setSecurityToken(securityToken);
        signatureVO.setDir(ossConfig.getUploadDir());
        signatureVO.setHost(ossConfig.getHost());
        signatureVO.setBucket(ossConfig.getBucket());
        signatureVO.setMaxFileSize(ossConfig.getMaxFileSize());

        log.info("成功生成OSS PostObject签名");
        return signatureVO;
    }

    /**
     * 生成过期时间字符串
     *
     * @param seconds 有效时长（秒）
     * @return ISO8601格式的时间字符串
     */
    public static String generateExpiration(long seconds) {
        long now = Instant.now().getEpochSecond();
        long expirationTime = now + seconds;
        Instant instant = Instant.ofEpochSecond(expirationTime);
        ZoneId zone = ZoneOffset.UTC;
        ZonedDateTime zonedDateTime = instant.atZone(zone);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        return zonedDateTime.format(formatter);
    }

    /**
     * 获取STS凭证
     *
     * @return STS凭证
     * @throws Exception 获取失败时抛出异常
     */
    private AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials getCredentials() throws Exception {
        Client client = createStsClient();
        AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest()
                .setRoleArn(ossConfig.getRoleArn())
                .setRoleSessionName(ossConfig.getRoleSessionName())
                .setDurationSeconds(ossConfig.getExpireTime().intValue());

        RuntimeOptions runtime = new RuntimeOptions();

        try {
            AssumeRoleResponse response = client.assumeRoleWithOptions(assumeRoleRequest, runtime);
            return response.body.credentials;
        } catch (TeaException error) {
            log.error("获取STS凭证失败: {}", error.getMessage());
            log.error("诊断地址: {}", error.getData().get("Recommend"));
            throw new RuntimeException("获取STS凭证失败: " + error.getMessage(), error);
        } catch (Exception error) {
            log.error("获取STS凭证异常: {}", error.getMessage(), error);
            throw new RuntimeException("获取STS凭证异常: " + error.getMessage(), error);
        }
    }

    /**
     * 创建STS客户端
     *
     * @return STS客户端
     * @throws Exception 创建失败时抛出异常
     */
    private Client createStsClient() throws Exception {
        Config config = new Config();

        // 优先使用配置文件中的配置，如果没有则使用环境变量
        if (StringUtils.hasText(ossConfig.getAccessKeyId())) {
            config.setAccessKeyId(ossConfig.getAccessKeyId());
        } else {
            config.setAccessKeyId(System.getenv("OSS_ACCESS_KEY_ID"));
        }

        if (StringUtils.hasText(ossConfig.getAccessKeySecret())) {
            config.setAccessKeySecret(ossConfig.getAccessKeySecret());
        } else {
            config.setAccessKeySecret(System.getenv("OSS_ACCESS_KEY_SECRET"));
        }

        config.endpoint = ossConfig.getStsEndpoint();
        return new Client(config);
    }

    /**
     * 创建上传策略
     *
     * @param securityToken STS安全令牌
     * @param xOssCredential OSS凭证
     * @param xOssDate OSS日期
     * @return 策略Map
     */
    private Map<String, Object> createPolicy(String securityToken, String xOssCredential, String xOssDate) {
        Map<String, Object> policy = new HashMap<>();
        policy.put("expiration", generateExpiration(ossConfig.getExpireTime()));

        List<Object> conditions = new ArrayList<>();

        // bucket条件
        Map<String, String> bucketCondition = new HashMap<>();
        bucketCondition.put("bucket", ossConfig.getBucket());
        conditions.add(bucketCondition);

        // 安全令牌条件
        Map<String, String> securityTokenCondition = new HashMap<>();
        securityTokenCondition.put("x-oss-security-token", securityToken);
        conditions.add(securityTokenCondition);

        // 签名版本条件
        Map<String, String> signatureVersionCondition = new HashMap<>();
        signatureVersionCondition.put("x-oss-signature-version", "OSS4-HMAC-SHA256");
        conditions.add(signatureVersionCondition);

        // 凭证条件
        Map<String, String> credentialCondition = new HashMap<>();
        credentialCondition.put("x-oss-credential", xOssCredential);
        conditions.add(credentialCondition);

        // 日期条件
        Map<String, String> dateCondition = new HashMap<>();
        dateCondition.put("x-oss-date", xOssDate);
        conditions.add(dateCondition);

        // 文件大小限制
        conditions.add(Arrays.asList("content-length-range", 1, ossConfig.getMaxFileSize()));

        // 成功状态码
        conditions.add(Arrays.asList("eq", "$success_action_status", "200"));

        // 文件路径前缀限制
        conditions.add(Arrays.asList("starts-with", "$key", ossConfig.getUploadDir()));

        policy.put("conditions", conditions);
        return policy;
    }

    /**
     * 计算签名密钥
     *
     * @param accessKeySecret 访问密钥Secret
     * @param date 日期字符串
     * @return 签名密钥
     */
    private byte[] calculateSigningKey(String accessKeySecret, String date) {
        byte[] dateKey = hmacSha256(("aliyun_v4" + accessKeySecret).getBytes(), date);
        byte[] dateRegionKey = hmacSha256(dateKey, ossConfig.getRegion());
        byte[] dateRegionServiceKey = hmacSha256(dateRegionKey, "oss");
        return hmacSha256(dateRegionServiceKey, "aliyun_v4_request");
    }

    /**
     * HMAC-SHA256计算
     *
     * @param key 密钥
     * @param data 数据
     * @return 计算结果
     */
    private static byte[] hmacSha256(byte[] key, String data) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKeySpec);
            return mac.doFinal(data.getBytes());
        } catch (Exception e) {
            throw new RuntimeException("HMAC-SHA256计算失败", e);
        }
    }
}
