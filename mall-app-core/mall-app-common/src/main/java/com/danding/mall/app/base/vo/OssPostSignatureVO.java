package com.danding.mall.app.base.vo;

import lombok.Data;

/**
 * OSS PostObject签名响应VO
 * 
 * <AUTHOR>
 */
@Data
public class OssPostSignatureVO {

    /**
     * 签名版本
     */
    private String version;

    /**
     * 策略（Base64编码）
     */
    private String policy;

    /**
     * OSS凭证
     */
    private String xOssCredential;

    /**
     * OSS日期
     */
    private String xOssDate;

    /**
     * 签名
     */
    private String signature;

    /**
     * 安全令牌
     */
    private String securityToken;

    /**
     * 上传目录前缀
     */
    private String dir;

    /**
     * OSS域名
     */
    private String host;

    /**
     * OSS Bucket名称
     */
    private String bucket;

    /**
     * 文件大小限制（字节）
     */
    private Long maxFileSize;
}
