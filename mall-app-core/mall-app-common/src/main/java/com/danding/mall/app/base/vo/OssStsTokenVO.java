package com.danding.mall.app.base.vo;

import lombok.Data;

/**
 * OSS STS临时凭证响应VO
 * 
 * <AUTHOR>
 */
@Data
public class OssStsTokenVO {

    /**
     * 临时访问密钥ID
     */
    private String accessKeyId;

    /**
     * 临时访问密钥Secret
     */
    private String accessKeySecret;

    /**
     * 安全令牌
     */
    private String securityToken;

    /**
     * 凭证过期时间
     */
    private String expiration;

    /**
     * OSS Bucket名称
     */
    private String bucket;

    /**
     * OSS区域
     */
    private String region;

    /**
     * OSS域名
     */
    private String host;

    /**
     * 上传目录前缀
     */
    private String uploadDir;

    /**
     * 文件大小限制（字节）
     */
    private Long maxFileSize;
}
