# OSS配置示例
# 请将此配置添加到您的application.yml文件中

aliyun:
  oss:
    # 阿里云AccessKey ID（建议使用环境变量）
    access-key-id: ${OSS_ACCESS_KEY_ID:your-access-key-id}
    # 阿里云AccessKey Secret（建议使用环境变量）
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:your-access-key-secret}
    # STS角色ARN（建议使用环境变量）
    role-arn: ${OSS_STS_ROLE_ARN:acs:ram::your-account-id:role/your-role-name}
    # OSS Bucket名称
    bucket: your-bucket-name
    # OSS区域
    region: cn-hangzhou
    # OSS域名
    host: https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com
    # 上传目录前缀
    upload-dir: uploads
    # STS临时凭证过期时间（秒）
    expire-time: 3600
    # 文件大小限制（字节，默认10MB）
    max-file-size: ********
    # STS端点
    sts-endpoint: sts.cn-hangzhou.aliyuncs.com
    # 角色会话名称
    role-session-name: mall-app-session

# 环境变量配置说明：
# OSS_ACCESS_KEY_ID: 阿里云AccessKey ID
# OSS_ACCESS_KEY_SECRET: 阿里云AccessKey Secret
# OSS_STS_ROLE_ARN: STS角色ARN，格式：acs:ram::账号ID:role/角色名称
