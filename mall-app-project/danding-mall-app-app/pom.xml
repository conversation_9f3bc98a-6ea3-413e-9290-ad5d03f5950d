<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.danding.mall.app</groupId>
        <artifactId>mall-app-project</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>danding-mall-app-app</artifactId>
    <packaging>jar</packaging>
    <name>danding-mall-app-app</name>

    <dependencies>
        <!--**************** 一方包 ****************-->
        <dependency>
            <groupId>com.danding.mall.app</groupId>
            <artifactId>danding-mall-app-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.mall.app</groupId>
            <artifactId>danding-mall-app-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.mall.app</groupId>
            <artifactId>app-WeCom</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding.mall.app</groupId>
            <artifactId>app-wechat</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding.mall.app</groupId>
            <artifactId>sdk-yang800</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.danding.mall.app</groupId>
            <artifactId>mall-app-channel-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--**************** 三方包：常用工具 ****************-->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>6.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.mall.app</groupId>
            <artifactId>sdk-parana</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>5.1.1</version>
            <scope>compile</scope>
        </dependency>
        <!--    阿里云OSS    -->
        <!-- https://mvnrepository.com/artifact/com.aliyun/credentials-java -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>credentials-java</artifactId>
            <version>0.3.4</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.kms</groupId>
            <artifactId>kms-transfer-client</artifactId>
            <version>0.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.17.4</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>sts20150401</artifactId>
            <version>1.1.6</version>
        </dependency>
    </dependencies>
</project>
