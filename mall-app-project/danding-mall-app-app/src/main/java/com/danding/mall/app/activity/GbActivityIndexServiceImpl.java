package com.danding.mall.app.activity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.danding.core.client.PageDTO;
import com.danding.mall.app.activity.api.IGbActivityIndexService;
import com.danding.mall.app.activity.dto.*;
import com.danding.mall.app.activity.dto.data.*;
import com.danding.mall.app.activity.enums.GbGroupStatusEnum;
import com.danding.mall.app.activity.enums.GbGroupStatusSelectEnum;
import com.danding.mall.app.activity.vo.request.GbActivityIndexReq;
import com.danding.mall.app.activity.vo.response.GbActivityIndexCurveResp;
import com.danding.mall.app.activity.vo.response.GbActivityIndexResp;
import com.danding.mall.app.activity.vo.response.GbActivitySkuIndexResp;
import com.danding.mall.app.activity.vo.response.GbGroupStatusInfoResp;
import com.danding.mall.app.base.session.helper.ShopHelper;
import com.danding.mall.app.domain.activity.gateway.*;
import com.danding.mall.app.domain.inventory.gateway.IMerchantCommodityInventoryGateway;
import com.danding.mall.app.domain.order.gateway.IShopOrdersGateway;
import com.danding.mall.app.domain.order.gateway.ISkuOrderGateway;
import com.danding.mall.app.domain.sku.gateway.ISkusGateway;
import com.danding.mall.app.domain.user.gateway.IFamilyUserGateway;
import com.danding.mall.app.inventory.dto.MerchantCommodityInventoryQry;
import com.danding.mall.app.inventory.dto.data.MerchantCommodityInventoryDTO;
import com.danding.mall.app.sku.dto.SkusQry;
import com.danding.mall.app.sku.dto.data.SkusDTO;
import com.danding.mall.app.order.dto.SkuOrderQry;
import com.danding.mall.app.order.dto.data.ShopOrderDTO;
import com.danding.mall.app.order.dto.data.SkuOrderDTO;
import com.danding.mall.app.user.dto.data.FamilyUserDTO;
import com.danding.mall.app.utils.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <p>
    * 拼团活动_活动维度统计 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Slf4j
@Service
public class GbActivityIndexServiceImpl implements IGbActivityIndexService {

    @Resource
    private IGbActivityIndexGateway gbActivityIndexGateway;
    @Resource
    private IGbActivitySkuIndexGateway gbActivitySkuIndexGateway;
    @Resource
    private IGbGroupInfoGateway gbGroupInfoGateway;
    @Resource
    private IGbGroupMemberGateway gbGroupMemberGateway;
    @Resource
    private IGbActivityConfigExtendGateway gbActivityConfigExtendGateway;
    @Resource
    private IMerchantCommodityInventoryGateway merchantCommodityInventoryGateway;
    @Resource
    private ISkuOrderGateway skuOrderGateway;
    @Resource
    private IFamilyUserGateway familyUserGateway;
    @Resource
    private ISkusGateway skusGateway;

    @Override
    public GbActivityIndexResp gmvCount(Long activityId) {
        Assert.notNull(activityId, "activity id must not be null");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbActivityIndexResp indexResp = new GbActivityIndexResp();
        //有效GMV
        GbActivitySkuIndexQry indexQry = new GbActivitySkuIndexQry();
        indexQry.setActivityId(activityId);
        indexQry.setShopId(shopId);
        BigDecimal netGmv = gbActivitySkuIndexGateway.sumNetGmv(indexQry);
        indexResp.setGmvEffectiveCount(netGmv);
        if (Objects.nonNull(netGmv) && netGmv.intValue() > 0) {
            indexResp.setGmvEffectiveCount(netGmv.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        //退款GMV
        BigDecimal doubleGmv = gbActivitySkuIndexGateway.sumDoubleGmv(indexQry);
        indexResp.setGmvRefundCount(doubleGmv);
        if (Objects.nonNull(doubleGmv) && doubleGmv.intValue() > 0) {
            indexResp.setGmvRefundCount(doubleGmv.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        return indexResp;
    }

    @Override
    public GbActivityIndexResp groupCount(Long activityId) {
        Assert.notNull(activityId, "activity id must not be null");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbActivityIndexResp indexResp = new GbActivityIndexResp();
        //正在成团数
        GbGroupInfoQry groupInfoQry = new GbGroupInfoQry();
        groupInfoQry.setActivityId(activityId);
        groupInfoQry.setShopId(shopId);
        groupInfoQry.setGroupStatusList(Arrays.asList(1, 2, 5));
        Long countIng = gbGroupInfoGateway.count(groupInfoQry);
        indexResp.setGroupCountIng(Objects.isNull(countIng) ? 0 : countIng.intValue());

        //成功成团数量
        GbActivityIndexQry indexQry = new GbActivityIndexQry();
        indexQry.setActivityId(activityId);
        indexQry.setShopId(shopId);
        Integer successCount = gbActivityIndexGateway.sumGroupSuccessCount(indexQry);
        successCount = Objects.isNull(successCount) ? 0 : successCount;
        indexResp.setGroupSuccessCount(successCount);

        //累积成团数量,进行中+成功成团数
        indexResp.setGroupCount(indexResp.getGroupCountIng() + indexResp.getGroupSuccessCount());

        //成团基数
        GbActivityConfigExtendQry extendQry = new GbActivityConfigExtendQry();
        extendQry.setActivityId(activityId);
        extendQry.setShopId(shopId);
        extendQry.setParaCode("init_group_count");
        GbActivityConfigExtendDTO extendDTO = gbActivityConfigExtendGateway.selectOne(extendQry);
        if (Objects.nonNull(extendDTO) && StringUtils.isNotBlank(extendDTO.getParaValue())) {
            indexResp.setBaseCount(Integer.valueOf(extendDTO.getParaValue()));
        } else {
            indexResp.setBaseCount(0);
        }
        return indexResp;
    }

    @Override
    public GbActivityIndexResp memberCount(Long activityId) {
        Assert.notNull(activityId, "activity id must not be null");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbActivityIndexResp indexResp = new GbActivityIndexResp();
        //成功人数
        GbActivityIndexQry indexQry = new GbActivityIndexQry();
        indexQry.setActivityId(activityId);
        indexQry.setShopId(shopId);
        Integer memberCount = gbActivityIndexGateway.sumMemberCount(indexQry);
        indexResp.setMemberCount(memberCount);
        //新人
        Integer successNewUser = gbActivityIndexGateway.sumSuccessNewUser(indexQry);
        indexResp.setSuccessNewUser(successNewUser);
        return indexResp;
    }

    @Override
    public GbActivityIndexResp participateCount(Long activityId) {
        Assert.notNull(activityId, "activity id must not be null");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbActivityIndexResp indexResp = new GbActivityIndexResp();
        //参与人数
        GbActivityIndexQry indexQry = new GbActivityIndexQry();
        indexQry.setActivityId(activityId);
        indexQry.setShopId(shopId);
        Integer participateCount = gbActivityIndexGateway.sumParticipateCount(indexQry);
        indexResp.setParticipateCount(participateCount);
        //平台新人
        Integer platformNewUser = gbActivityIndexGateway.sumPlatformNewUser(indexQry);
        indexResp.setPlatformNewUser(platformNewUser);
        return indexResp;
    }

    @Override
    public GbActivityIndexResp groupSuccessRate(Long activityId) {
        Assert.notNull(activityId, "activity id must not be null");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbActivityIndexResp indexResp = new GbActivityIndexResp();
        indexResp.setGroupSuccessRate(BigDecimal.ZERO);
        //累积成团数量
        GbActivityIndexQry activityIndexQry = new GbActivityIndexQry();
        activityIndexQry.setActivityId(activityId);
        activityIndexQry.setShopId(shopId);
        GbActivityIndexDTO indexDTO = gbActivityIndexGateway.getLastOne(activityIndexQry);
        if (Objects.nonNull(indexDTO) && !Objects.equals(0L, indexDTO.getGroupCreateCount())) {
            //成功成团数
            GbActivityIndexQry indexQry = new GbActivityIndexQry();
            indexQry.setActivityId(activityId);
            indexQry.setShopId(shopId);
            Integer groupSuccessCount = gbActivityIndexGateway.sumGroupSuccessCount(indexQry);
            if (Objects.nonNull(groupSuccessCount) && !Objects.equals(0L, groupSuccessCount)) {
                BigDecimal groupSuccessRate = new BigDecimal(groupSuccessCount).multiply(new BigDecimal(100)).divide(new BigDecimal(indexDTO.getGroupCreateCount()), 2, RoundingMode.HALF_UP);
                indexResp.setGroupSuccessRate(groupSuccessRate);
            }
        }
        return indexResp;
    }

    @Override
    public Boolean baseCountModify(Long activityId, Long baseCount) {
        Assert.notNull(activityId, "activity id must not be null");
        Assert.notNull(baseCount, "团基础数量不能为空");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbActivityConfigExtendQry extendQry = new GbActivityConfigExtendQry();
        extendQry.setActivityId(activityId);
        extendQry.setShopId(shopId);
        extendQry.setParaCode("init_group_count");
        GbActivityConfigExtendDTO extendDTO = gbActivityConfigExtendGateway.selectOne(extendQry);
        if (Objects.isNull(extendDTO)) {
            extendDTO = new GbActivityConfigExtendDTO();
            extendDTO.setActivityId(activityId);
            extendDTO.setShopId(shopId);
            extendDTO.setParaCode("init_group_count");
            extendDTO.setParaValue(baseCount + "");
            return gbActivityConfigExtendGateway.insert(extendDTO);
        } else {
            extendDTO.setParaValue(baseCount + "");
            return gbActivityConfigExtendGateway.updateById(extendDTO.getId(), extendDTO);
        }
    }

    @Override
    public GbActivityIndexCurveResp groupCurve(Long activityId, Integer type, Integer dayTime) {
        Assert.notNull(activityId, "activity id must not be null");
        Assert.notNull(type, "曲线类型不能为空");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        //默认查询7天的数据
        Integer day = Objects.isNull(dayTime) || Objects.equals(1, dayTime) ? 7 : 30;
        Long statisticsTimeStart = MyDateUtil.beforeNumDays(new Date(), day,0);
        Long statisticsTimeEnd = MyDateUtil.beforeNumDays(new Date(), 1,0);

        String rangeTimeDesc = DateUtil.format(new Date(statisticsTimeStart), "yyyy/MM/dd") + "~" + DateUtil.format(new Date(statisticsTimeEnd), "yyyy/MM/dd");
        //1 gmv,2成团数量,3成团人数，4参与人数，5 成团率
        if (Objects.equals(1, type)) {
            GbActivitySkuIndexQry indexSkuQry = new GbActivitySkuIndexQry();
            indexSkuQry.setActivityId(activityId);
            indexSkuQry.setShopId(shopId);
            indexSkuQry.setStatisticsTimeStart(statisticsTimeStart);
            indexSkuQry.setStatisticsTimeEnd(statisticsTimeEnd);
            List<GbActivitySkuIndexDTO> skuIndexDTOList = gbActivitySkuIndexGateway.selectList(indexSkuQry);
            return buildActivitySkuIndexList(skuIndexDTOList, day, rangeTimeDesc);
        } else {
            GbActivityIndexQry indexQry = new GbActivityIndexQry();
            indexQry.setActivityId(activityId);
            indexQry.setShopId(shopId);
            indexQry.setStatisticsTimeStart(statisticsTimeStart);
            indexQry.setStatisticsTimeEnd(statisticsTimeEnd);
            List<GbActivityIndexDTO> indexDTOList = gbActivityIndexGateway.selectList(indexQry);
            return buildActivityIndexList(indexDTOList, type, day, rangeTimeDesc);
        }
    }

    /**
     * GMV组装
     *
     * @param skuIndexDTOList
     * @param day
     * @return
     */
    private GbActivityIndexCurveResp buildActivitySkuIndexList(List<GbActivitySkuIndexDTO> skuIndexDTOList, Integer day, String rangeTimeDesc) {
        Map<Long, List<GbActivitySkuIndexDTO>> skuIndexDTOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(skuIndexDTOList)) {
            skuIndexDTOMap = skuIndexDTOList.stream().collect(Collectors.groupingBy(GbActivitySkuIndexDTO::getStatisticsTime));
        }
        List<String> xDateList = new ArrayList<>();
        List<BigDecimal> yDateEffectiveList = new ArrayList<>();
        List<BigDecimal> yDateRefundList = new ArrayList<>();
        for (int i = 0; i < day; i++) {
            if (Objects.equals(30, day)) {
                if (i%2 == 0) {
                    buildActivitySkuIndexDTO(xDateList, yDateEffectiveList, yDateRefundList, skuIndexDTOMap, day - i - 1);
                }
            } else {
                buildActivitySkuIndexDTO(xDateList, yDateEffectiveList, yDateRefundList, skuIndexDTOMap, day - i);
            }
        }

        GbActivityIndexCurveResp activityIndexCurveResp = new GbActivityIndexCurveResp();
        activityIndexCurveResp.setXDateList(xDateList);
        List<List<BigDecimal>> yDateList = new ArrayList<>();
        yDateList.add(yDateEffectiveList);
        yDateList.add(yDateRefundList);
        activityIndexCurveResp.setYDateList(yDateList);
        activityIndexCurveResp.setRangeTimeDesc(rangeTimeDesc);
        return activityIndexCurveResp;
    }

    private void buildActivitySkuIndexDTO(List<String> xDateList, List<BigDecimal> yDateEffectiveList, List<BigDecimal> yDateRefundList, Map<Long, List<GbActivitySkuIndexDTO>> skuIndexDTOMap, Integer day) {
        Long statisticsTime = MyDateUtil.beforeNumDays(new Date(), day, 0);
        xDateList.add(DateUtil.format(new Date(statisticsTime), "MM/dd"));

        List<GbActivitySkuIndexDTO> skuIndexDTOList = skuIndexDTOMap.get(statisticsTime);
        if (CollectionUtil.isEmpty(skuIndexDTOList)) {//为查询到数据,直接给0
            yDateEffectiveList.add(BigDecimal.ZERO);
            yDateRefundList.add(BigDecimal.ZERO);
            return;
        }
        GbActivityIndexResp indexResp = new GbActivityIndexResp();
        indexResp.setGmvEffectiveCount(BigDecimal.ZERO);
        indexResp.setGmvRefundCount(BigDecimal.ZERO);
        for (GbActivitySkuIndexDTO skuIndexDTO : skuIndexDTOList) {
            indexResp.setGmvEffectiveCount(indexResp.getGmvEffectiveCount().add(new BigDecimal(skuIndexDTO.getNetGmv())));
            indexResp.setGmvRefundCount(indexResp.getGmvRefundCount().add(new BigDecimal(skuIndexDTO.getDoubleGmv())));
        }
        if (Objects.nonNull(indexResp.getGmvEffectiveCount()) && indexResp.getGmvEffectiveCount().intValue() > 0) {
            yDateEffectiveList.add(indexResp.getGmvEffectiveCount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        } else {
            yDateEffectiveList.add(BigDecimal.ZERO);
        }
        if (Objects.nonNull(indexResp.getGmvRefundCount()) && indexResp.getGmvRefundCount().intValue() > 0) {
            yDateRefundList.add(indexResp.getGmvRefundCount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        } else {
            yDateEffectiveList.add(BigDecimal.ZERO);
        }
    }

    /**
     * 团信息组装
     *
     * @param indexDTOList
     * @param type
     * @param day
     * @param rangeTimeDesc
     * @return
     */
    private GbActivityIndexCurveResp buildActivityIndexList(List<GbActivityIndexDTO> indexDTOList, Integer type, Integer day, String rangeTimeDesc){
        Map<Long, GbActivityIndexDTO> indexDTOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(indexDTOList)) {
            indexDTOMap = indexDTOList.stream().collect(Collectors.toMap(GbActivityIndexDTO::getStatisticsTime, Function.identity()));
        }
        List<String> xDateList = new ArrayList<>();
        List<BigDecimal> yDateList = new ArrayList<>();
        for (int i = 0; i < day; i++) {
            if (Objects.equals(30, day)) {
                if (i%2 == 0) {
                    buildActivityIndexDTO(xDateList, yDateList, indexDTOMap, day - i - 1, type);
                }
            } else {
                buildActivityIndexDTO(xDateList, yDateList, indexDTOMap, day - i, type);
            }
        }

        GbActivityIndexCurveResp gbActivityIndexCurveResp = new GbActivityIndexCurveResp();
        gbActivityIndexCurveResp.setXDateList(xDateList);
        gbActivityIndexCurveResp.setYDateList(Arrays.asList(yDateList));
        gbActivityIndexCurveResp.setRangeTimeDesc(rangeTimeDesc);
        return gbActivityIndexCurveResp;
    }

    /**
     * @param type 2成团数量,3成团人数，4参与人数，5 成团率
     */
    private void buildActivityIndexDTO(List<String> xDateList, List<BigDecimal> yDateList, Map<Long, GbActivityIndexDTO> indexDTOMap, Integer day, Integer type) {
        Long statisticsTime = MyDateUtil.beforeNumDays(new Date(), day, 0);
        xDateList.add(DateUtil.format(new Date(statisticsTime), "MM/dd"));

        GbActivityIndexDTO indexDTO = indexDTOMap.get(statisticsTime);
        if (Objects.isNull(indexDTO)) {//为查询到数据,直接给0
            yDateList.add(BigDecimal.ZERO);
            return;
        }
        if (Objects.equals(2, type)) {
            yDateList.add(new BigDecimal(indexDTO.getGroupSuccessCount()));
        } else if (Objects.equals(3, type)) {
            yDateList.add(new BigDecimal(indexDTO.getMemberCount()));
        } else if (Objects.equals(4, type)) {
            yDateList.add(new BigDecimal(indexDTO.getParticipateCount()));
        } else if (Objects.equals(5, type)) {
            yDateList.add(new BigDecimal(indexDTO.getGroupSuccessRate()));
        } else {
            throw new RuntimeException("曲线查询类型错误");
        }
    }

    @Override
    public PageDTO<GbGroupStatusInfoResp> groupStatusInfo(GbActivityIndexReq indexReq) {
        Assert.notNull(indexReq.getActivityId(), "activity id must not be null");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbGroupInfoQry infoQry = new GbGroupInfoQry();
        infoQry.setShopId(shopId);
        infoQry.setActivityId(indexReq.getActivityId());
        if (Objects.equals(2, indexReq.getGroupStatus())) {
            infoQry.setGroupStatusList(Arrays.asList(1, 2, 5));
        } else {
            infoQry.setGroupStatus(indexReq.getGroupStatus());
        }
        infoQry.setGroupTimeStart(indexReq.getGroupTimeStart());
        infoQry.setGroupTimeEnd(indexReq.getGroupTimeEnd());
        infoQry.setSuccessTimeStart(indexReq.getSuccessTimeStart());
        infoQry.setSuccessTimeEnd(indexReq.getSuccessTimeEnd());
        PageDTO<GbGroupInfoDTO> page = gbGroupInfoGateway.page(infoQry, indexReq);
        List<GbGroupInfoDTO> dataList = page.getDataList();

        List<GbGroupStatusInfoResp> respList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataList)) {
            Map<Long, List<GbGroupMemberDTO>> memberMap = new HashMap<>();
            Map<Long, List<SkuOrderDTO>> skuOrderMap = new HashMap<>();
            //查询团员信息
            List<Long> groupIdList = dataList.stream().map(GbGroupInfoDTO::getId).collect(Collectors.toList());
            GbGroupMemberQry memberQry = new GbGroupMemberQry();
            memberQry.setGroupIdList(groupIdList);
            List<GbGroupMemberDTO> memberDTOList = gbGroupMemberGateway.selectList(memberQry);
            if (CollectionUtil.isNotEmpty(memberDTOList)) {
                memberMap = memberDTOList.stream().collect(Collectors.groupingBy(GbGroupMemberDTO::getGroupId));
                //查询订单信息
                List<Long> orderIdList = memberDTOList.stream().map(GbGroupMemberDTO::getOrderId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(orderIdList)) {
                    List<SkuOrderDTO> skuOrderDTOList = skuOrderGateway.selectByOrderId(orderIdList);
                    skuOrderMap = skuOrderDTOList.stream().collect(Collectors.groupingBy(SkuOrderDTO::getOrderId));
                }
            }
            //组装页面返回值
            for (GbGroupInfoDTO gbGroupInfoDTO : dataList) {
                GbGroupStatusInfoResp resp = new GbGroupStatusInfoResp();
                resp.setGmvEffectiveCount(BigDecimal.ZERO);
                resp.setGmvRefundCount(BigDecimal.ZERO);
                if (Objects.nonNull(gbGroupInfoDTO.getGroupTime()) && !Objects.equals(0L, gbGroupInfoDTO.getGroupTime())) {
                    resp.setGroupTimeDesc(DateUtil.format(new Date(gbGroupInfoDTO.getGroupTime()), "yyyy-MM-dd HH:mm:ss"));
                }
                if (Objects.nonNull(gbGroupInfoDTO.getSuccessTime()) && !Objects.equals(0L, gbGroupInfoDTO.getSuccessTime())) {
                    resp.setSuccessTimeDesc(DateUtil.format(new Date(gbGroupInfoDTO.getSuccessTime()), "yyyy-MM-dd HH:mm:ss"));
                }
                if (Objects.equals(GbGroupStatusEnum.LEADER_PENDING_PAYMENT.getCode(), gbGroupInfoDTO.getGroupStatus())
                        || Objects.equals(GbGroupStatusEnum.IN_PROGRESS.getCode(), gbGroupInfoDTO.getGroupStatus())
                        || Objects.equals(GbGroupStatusEnum.GROUP_FULL.getCode(), gbGroupInfoDTO.getGroupStatus())) {
                    resp.setGroupStatusDesc("开团中");
                } else {
                    resp.setGroupStatusDesc(GbGroupStatusSelectEnum.fromCode(gbGroupInfoDTO.getGroupStatus()).getDescription());
                }
                List<GbGroupMemberDTO> groupMemberDTOList = memberMap.get(gbGroupInfoDTO.getId());
                if (CollectionUtil.isNotEmpty(groupMemberDTOList)) {
                    for (GbGroupMemberDTO groupMemberDTO : groupMemberDTOList) {
                        //昵称
                        FamilyUserDTO familyUserDTO = familyUserGateway.selectFamilyUser(shopId, groupMemberDTO.getGroupUserId());
                        if (Objects.nonNull(familyUserDTO)) {
                            if(Objects.equals(1, groupMemberDTO.getMemberRole())) {
                                resp.setMemberHeadName(familyUserDTO.getNickName());
                            } else {
                                if (StringUtils.isNotBlank(resp.getMemberName())) {
                                    resp.setMemberName(resp.getMemberName() + ";" + familyUserDTO.getNickName());
                                } else {
                                    resp.setMemberName(familyUserDTO.getNickName());
                                }
                            }
                        }
                        //GMV
                        List<SkuOrderDTO> skuOrderDTOList = skuOrderMap.get(groupMemberDTO.getOrderId());
                        if (CollectionUtil.isNotEmpty(skuOrderDTOList)) {
                            List<Integer> list = Arrays.asList(2, 3, -3, -4, -5, -7, -8, -9, -10, -19); //有效GMV状态
                            for (SkuOrderDTO skuOrderDTO : skuOrderDTOList) {
                                Integer status = skuOrderDTO.getStatus();
                                if (list.contains(status) && Objects.nonNull(groupMemberDTO.getDeliveryTime())) {
                                    resp.setGmvEffectiveCount(resp.getGmvEffectiveCount().add(new BigDecimal(skuOrderDTO.getFee()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
                                } else if (Objects.equals(-6, status) && Objects.nonNull(groupMemberDTO.getRefundTime())) { //退款GMV状态
                                    resp.setGmvRefundCount(resp.getGmvRefundCount().add(new BigDecimal(skuOrderDTO.getFee()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
                                }
                            }

                        }
                    }
                }
                respList.add(resp);
            }
        }
        PageDTO<GbGroupStatusInfoResp> pageDTO = new PageDTO<>();
        return pageDTO.vo(respList, indexReq, page.getPage().getTotalCount());
    }

    @Override
    public PageDTO<GbActivitySkuIndexResp> activitySkuIndex(GbActivityIndexReq indexReq) {
        Assert.notNull(indexReq.getActivityId(), "activity id must not be null");
        Long shopId = ShopHelper.getId();
        if (Objects.isNull(shopId) || Objects.equals(0L, shopId)) {
            return null;
        }
        GbActivitySkuIndexQry indexSkuQry = new GbActivitySkuIndexQry();
        indexSkuQry.setActivityId(indexReq.getActivityId());
        indexSkuQry.setShopId(shopId);
        indexSkuQry.setSkuNameLike(indexReq.getSkuNameLike());
        PageDTO<GbActivitySkuIndexDTO> page = gbActivitySkuIndexGateway.pageList(indexSkuQry, indexReq);
        List<GbActivitySkuIndexDTO> dataList = page.getDataList();

        Map<Long, GbActivitySkuIndexResp> respMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dataList)) {
            List<Long> skuIdList = dataList.stream().map(GbActivitySkuIndexDTO::getSkuId).collect(Collectors.toList());
            indexSkuQry.setSkuIdList(skuIdList);
            List<GbActivitySkuIndexDTO> skuIndexDTOList = gbActivitySkuIndexGateway.selectList(indexSkuQry);

            MerchantCommodityInventoryQry inventoryQry = new MerchantCommodityInventoryQry();
            inventoryQry.setShopId(shopId);
            inventoryQry.setActivityId(indexReq.getActivityId());

            for (GbActivitySkuIndexDTO skuIndexDTO : skuIndexDTOList) {
                GbActivitySkuIndexResp skuIndexResp = respMap.get(skuIndexDTO.getSkuId());
                if (Objects.isNull(skuIndexResp)) {
                    skuIndexResp = new GbActivitySkuIndexResp();
                    skuIndexResp.setSkuName(skuIndexDTO.getSkuName());
                    skuIndexResp.setSaleCount(skuIndexDTO.getSaleCount());
                    skuIndexResp.setRefundCount(skuIndexDTO.getRetureCount());
                    skuIndexResp.setGmvEffectiveCount(new BigDecimal(skuIndexDTO.getNetGmv()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                    skuIndexResp.setGmvRefundCount(new BigDecimal(skuIndexDTO.getDoubleGmv()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                    skuIndexResp.setAvailableQty(0);
                    skuIndexResp.setInitQty(0);
                    //查询库存
                    inventoryQry.setSkuId(skuIndexDTO.getSkuId());
                    MerchantCommodityInventoryDTO inventoryDTO = merchantCommodityInventoryGateway.selectOne(inventoryQry);
                    if (Objects.nonNull(inventoryDTO)) {
                        skuIndexResp.setAvailableQty(inventoryDTO.getAvailableQty());
                        skuIndexResp.setInitQty(inventoryDTO.getInitQty());
                    }
                    respMap.put(skuIndexDTO.getSkuId(), skuIndexResp);
                } else {
                    skuIndexResp.setSaleCount(skuIndexResp.getSaleCount() + skuIndexDTO.getSaleCount());
                    skuIndexResp.setRefundCount(skuIndexResp.getRefundCount() + skuIndexDTO.getRetureCount());
                    skuIndexResp.setGmvEffectiveCount(skuIndexResp.getGmvEffectiveCount().add(new BigDecimal(skuIndexDTO.getNetGmv()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
                    skuIndexResp.setGmvRefundCount(skuIndexResp.getGmvRefundCount().add(new BigDecimal(skuIndexDTO.getDoubleGmv()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
                }
            }
        }
        ArrayList<GbActivitySkuIndexResp> respArrayList = new ArrayList<>(respMap.values());
        respArrayList.sort((p1, p2) -> Integer.compare(p2.getSaleCount(), p1.getSaleCount()));

        PageDTO<GbActivitySkuIndexResp> pageDTO = new PageDTO<>();
        return pageDTO.vo(respArrayList, indexReq, page.getPage().getTotalCount());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void statisticsGroup(GbActivityInfoDTO activityInfoDTO, Long dayTime, Integer day) {
        try {
            long start = System.currentTimeMillis();
            log.info("statisticsGroup activityId={} 活动看板统计入参={}", activityInfoDTO.getId(), JSON.toJSONString(activityInfoDTO));
            Long activityId = activityInfoDTO.getId();
            if (Objects.isNull(activityId) || Objects.isNull(activityInfoDTO.getShopId())) {
                return;
            }
            if (Objects.isNull(day)) {
                day = 1;
            }
            Long endTime;
            Long startTime;
            if (Objects.isNull(dayTime)) {
                startTime = MyDateUtil.beforeNumDays(new Date(), day, 0);
                endTime = MyDateUtil.beforeNumDays(new Date(), day, 1);
            } else {
                startTime = dayTime;
                endTime = MyDateUtil.beforeNumDays(new Date(dayTime), day, 1);
            }

            GbActivityIndexQry indexQry = new GbActivityIndexQry();
            indexQry.setActivityId(activityId);
            indexQry.setStatisticsTime(startTime);
            GbActivityIndexDTO gbActivityIndexDTO = gbActivityIndexGateway.selectOne(indexQry);
            if (Objects.isNull(gbActivityIndexDTO)) {
                gbActivityIndexDTO = new GbActivityIndexDTO();
            }
            gbActivityIndexDTO.setShopId(activityInfoDTO.getShopId());
            gbActivityIndexDTO.setActivityId(activityId);
            gbActivityIndexDTO.setStatisticsTime(startTime);
            gbActivityIndexDTO.setGroupSuccessRate(0d);
            gbActivityIndexDTO.setPlatformNewUser(0);
            gbActivityIndexDTO.setSuccessNewUser(0);
            gbActivityIndexDTO.setMemberCount(0);
            gbActivityIndexDTO.setParticipateCount(0);

            GbGroupInfoQry qry = new GbGroupInfoQry();
            qry.setActivityId(activityId);
            Long count = gbGroupInfoGateway.count(qry);
            //总团数
            gbActivityIndexDTO.setGroupCreateCount(count.intValue());
            //查询指定时间范围成团成功数
            qry.setGroupStatus(3);
            qry.setSuccessTimeStart(startTime);
            qry.setSuccessTimeEnd(endTime);
            List<GbGroupInfoDTO> groupInfoDTOList = gbGroupInfoGateway.selectList(qry);
            if (CollectionUtil.isNotEmpty(groupInfoDTOList)) {
                //成团成功数
                Integer groupStatusCount = groupInfoDTOList.size();
                gbActivityIndexDTO.setGroupSuccessCount(groupStatusCount);
                //成功率
                BigDecimal groupSuccessRate = new BigDecimal(groupStatusCount).multiply(new BigDecimal(100)).divide(new BigDecimal(count), 2, RoundingMode.HALF_UP);
                gbActivityIndexDTO.setGroupSuccessRate(groupSuccessRate.doubleValue());
                //查询成团成员
                List<Long> groupIdList = groupInfoDTOList.stream().map(GbGroupInfoDTO::getId).collect(Collectors.toList());
                GbGroupMemberQry memberQry = new GbGroupMemberQry();
                memberQry.setActivityId(activityId);
                memberQry.setGroupIdList(groupIdList);
                memberQry.setStatusList(Arrays.asList(4,5,6));//查询有效团员状态
                List<GbGroupMemberDTO> groupMemberDTOList = gbGroupMemberGateway.selectList(memberQry);
                //拼团成功人数
                gbActivityIndexDTO.setMemberCount(groupMemberDTOList.size());
                List<Long> newUserIdList = new ArrayList<>();
                for (GbGroupMemberDTO memberDTO : groupMemberDTOList) {
                    //是否新客
                    boolean isNewUser = familyUserGateway.checkByActivityIdAndUserId(memberDTO.getShopId(), memberDTO.getGroupUserId(), memberDTO.getActivityId(), memberDTO.getGroupId(), activityInfoDTO.getMarketingToolId());
                    log.info(DateUtil.formatDate(new Date(startTime))+ "memberId={},是否成团新客={}", memberDTO.getId(), isNewUser);
                    if (isNewUser && !newUserIdList.contains(memberDTO.getGroupUserId())) {
                        //成团新客
                        gbActivityIndexDTO.setSuccessNewUser(gbActivityIndexDTO.getSuccessNewUser() + 1);
                        newUserIdList.add(memberDTO.getGroupUserId());//同一个用户能在同一个团里，进进出出,防止多次入团新人统计多次
                    }
                }
            }
            //总的参与活动人数
            //团员在同一个团,按一次,不同团,按多次
            GbGroupMemberQry memberQry = new GbGroupMemberQry();
            memberQry.setActivityId(activityId);
            memberQry.setMemberRole(2);
            List<GbGroupMemberDTO> memberList = gbGroupMemberGateway.selectList(memberQry);
            if (CollectionUtil.isNotEmpty(memberList)) {
                Map<Long, List<GbGroupMemberDTO>> listMap = memberList.stream().collect(Collectors.groupingBy(GbGroupMemberDTO::getGroupId));
                for (Long key : listMap.keySet()) {
                    List<GbGroupMemberDTO> groupMemberDTOList = listMap.get(key);
                    Set<Long> collect = groupMemberDTOList.stream().map(GbGroupMemberDTO::getGroupUserId).collect(Collectors.toSet());
                    gbActivityIndexDTO.setParticipateCount(gbActivityIndexDTO.getParticipateCount() + collect.size());
                }
            }
            //团长多次按1次计算
            memberQry.setMemberRole(1);
            List<GbGroupMemberDTO> memberDTOList = gbGroupMemberGateway.selectList(memberQry);
            if (CollectionUtil.isNotEmpty(memberDTOList)) {
                Set<Long> collect = memberDTOList.stream().map(GbGroupMemberDTO::getGroupUserId).collect(Collectors.toSet());
                gbActivityIndexDTO.setParticipateCount(gbActivityIndexDTO.getParticipateCount() + collect.size());
            }
            //当天参与的平台新客
            Long platformNewUser = familyUserGateway.countByActivityIdAndTime(activityInfoDTO.getShopId(), activityId, activityInfoDTO.getMarketingToolId(), startTime, endTime);
            log.info(DateUtil.formatDateTime(new Date(startTime)) + "查询平台新客数量={}", platformNewUser);
            gbActivityIndexDTO.setPlatformNewUser(platformNewUser.intValue());

            if (Objects.nonNull(gbActivityIndexDTO.getId())) {
                gbActivityIndexGateway.updateById(gbActivityIndexDTO.getId(), gbActivityIndexDTO);
            } else {
                gbActivityIndexGateway.insert(gbActivityIndexDTO);
            }
            log.info("activityId={}团统计耗时:{}毫秒", activityId, System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("{}统计团信息异常ex:", activityInfoDTO.getId(), e);
            e.printStackTrace();
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void statisticsGmv(GbActivityInfoDTO activityInfoDTO, Long dayTime, Integer day) {
        try {
            long start = System.currentTimeMillis();
            log.info("statisticsGmv activityId={} 活动看板GMV统计入参={}", activityInfoDTO.getId(), JSON.toJSONString(activityInfoDTO));
            Long activityId = activityInfoDTO.getId();
            if (Objects.isNull(activityId) || Objects.isNull(activityInfoDTO.getShopId())) {
                return;
            }
            if (Objects.isNull(day)) {
                day = 1;
            }
            Long endTime;
            Long startTime;
            if (Objects.isNull(dayTime)) {
                startTime = MyDateUtil.beforeNumDays(new Date(), day, 0);
                endTime = MyDateUtil.beforeNumDays(new Date(), day, 1);
            } else {
                startTime = dayTime;
                endTime = MyDateUtil.beforeNumDays(new Date(dayTime), day, 1);
            }
            //有效GMV
            GbGroupMemberQry memberQry = new GbGroupMemberQry();
            memberQry.setActivityId(activityId);
            memberQry.setDeliveryTimeStart(startTime);
            memberQry.setDeliveryTimeEnd(endTime);
            List<GbGroupMemberDTO> memberDTOList = gbGroupMemberGateway.selectList(memberQry);
            log.info("{}有效GMV成员信息={}", activityId, JSON.toJSONString(memberDTOList));

            //退款GMV
            memberQry.setDeliveryTimeStart(null);
            memberQry.setDeliveryTimeEnd(null);
            memberQry.setRefundTimeStart(startTime);
            memberQry.setRefundTimeEnd(endTime);
            List<GbGroupMemberDTO> refundMemberDTOList = gbGroupMemberGateway.selectList(memberQry);
            log.info("{}退款GMV成员信息={}", activityId, JSON.toJSONString(refundMemberDTOList));
            if (CollectionUtil.isNotEmpty(refundMemberDTOList)) {
                memberDTOList.addAll(refundMemberDTOList);
            }
            if (CollectionUtil.isEmpty(memberDTOList)) {
                return;
            }
            List<Long> orderIdList = memberDTOList.stream().map(GbGroupMemberDTO::getOrderId).collect(Collectors.toList());
            SkuOrderQry skuOrderQry = new SkuOrderQry();
            skuOrderQry.setOrderIdList(orderIdList);
            List<SkuOrderDTO> orderDTOList = skuOrderGateway.selectList(skuOrderQry);

            List<Long> skuIdList = orderDTOList.stream().map(SkuOrderDTO::getSkuId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(skuIdList)) {
                return;
            }
            //sku查询
            SkusQry skuQry = new SkusQry();
            skuQry.setIdList(skuIdList);
            List<SkusDTO> skusDTOList = skusGateway.selectList(skuQry);
            Map<Long, String> nameMap = skusDTOList.stream().filter(skusDTO -> StringUtils.isNotBlank(skusDTO.getSpecification())).collect(Collectors.toMap(SkusDTO::getId, SkusDTO::getSpecification));

            //历史数据查询
            GbActivitySkuIndexQry indexQry = new GbActivitySkuIndexQry();
            indexQry.setActivityId(activityId);
            indexQry.setSkuIdList(skuIdList);
            indexQry.setStatisticsTime(startTime);
            List<GbActivitySkuIndexDTO> skuIndexDTOList = gbActivitySkuIndexGateway.selectList(indexQry);
            Map<Long, GbActivitySkuIndexDTO> skuIndexDTOMap = skuIndexDTOList.stream().collect(Collectors.toMap(GbActivitySkuIndexDTO::getSkuId, Function.identity()));

            Map<Long, GbActivitySkuIndexDTO> modifyMap = new HashMap<>();
            Map<Long, GbActivitySkuIndexDTO> addMap = new HashMap<>();
            for (SkuOrderDTO skuOrderDTO : orderDTOList) {
                GbActivitySkuIndexDTO indexDTO = skuIndexDTOMap.get(skuOrderDTO.getSkuId());
                if (Objects.nonNull(indexDTO)) {//存在历史数据,更新
                    GbActivitySkuIndexDTO skuIndex = modifyMap.get(skuOrderDTO.getSkuId());
                    if (Objects.isNull(skuIndex)) {
                        skuIndex = buildNewSkuIndex(indexDTO.getId());
                        modifyMap.put(skuOrderDTO.getSkuId(), skuIndex);
                    }
                    String name = nameMap.get(skuOrderDTO.getSkuId());
                    skuIndex.setSkuName(skuOrderDTO.getItemName() + (StringUtils.isBlank(name) ? "" : name));
                    buildNewSkuIndexData(skuIndex, skuOrderDTO, activityId, startTime, activityInfoDTO.getShopId());
                } else {//不存在历史数据,新增
                    GbActivitySkuIndexDTO skuIndex = addMap.get(skuOrderDTO.getSkuId());
                    if (Objects.isNull(skuIndex)) {
                        skuIndex = buildNewSkuIndex(null);
                        addMap.put(skuOrderDTO.getSkuId(), skuIndex);
                    }
                    String name = nameMap.get(skuOrderDTO.getSkuId());
                    skuIndex.setSkuName(skuOrderDTO.getItemName() + (StringUtils.isBlank(name) ? "" : name));
                    buildNewSkuIndexData(skuIndex, skuOrderDTO, activityId, startTime, activityInfoDTO.getShopId());
                }
            }
            if (CollectionUtil.isNotEmpty(modifyMap)) {
                gbActivitySkuIndexGateway.updateBatch(new ArrayList<>(modifyMap.values()));
            }
            if (CollectionUtil.isNotEmpty(addMap)) {
                gbActivitySkuIndexGateway.insertBatch(new ArrayList<>(addMap.values()));
            }
            log.info("activityId={}GMV统计耗时:{}毫秒", activityId, System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("{}统计GMV异常ex:", activityInfoDTO.getId(), e);
            e.printStackTrace();
            throw e;
        }
    }

    private GbActivitySkuIndexDTO buildNewSkuIndex(Long id){
        GbActivitySkuIndexDTO indexDTO = new GbActivitySkuIndexDTO();
        indexDTO.setId(id);
        //设置初始值
        indexDTO.setGmv(0d);
        indexDTO.setNetGmv(0d);
        indexDTO.setDoubleGmv(0d);
        indexDTO.setSaleCount(0);
        indexDTO.setRetureCount(0);
        indexDTO.setRetureRate(0d);
        return indexDTO;
    }

    private void buildNewSkuIndexData(GbActivitySkuIndexDTO indexDTO, SkuOrderDTO skuOrderDTO, Long activityId, Long startTime, Long shopId){
        //设置数据
        indexDTO.setActivityId(activityId);
        indexDTO.setStatisticsTime(startTime);
        indexDTO.setShopId(shopId);
        indexDTO.setSpuId(skuOrderDTO.getItemId());
        indexDTO.setSkuId(skuOrderDTO.getSkuId());
        indexDTO.setGmv(new BigDecimal(indexDTO.getGmv()).add(new BigDecimal(skuOrderDTO.getFee())).doubleValue());

        List<Integer> list = Arrays.asList(2, 3, -3, -4, -5, -7, -8, -9, -10, -19);//有效GMV状态
        if (list.contains(skuOrderDTO.getStatus())) {
            indexDTO.setNetGmv(new BigDecimal(indexDTO.getNetGmv()).add(new BigDecimal(skuOrderDTO.getFee())).doubleValue());
        } else if (Objects.equals(-6, skuOrderDTO.getStatus())){
            indexDTO.setDoubleGmv(new BigDecimal(indexDTO.getDoubleGmv()).add(new BigDecimal(skuOrderDTO.getFee())).doubleValue());
            indexDTO.setRetureCount(indexDTO.getRetureCount() + skuOrderDTO.getQuantity().intValue());
        }
        indexDTO.setSaleCount(indexDTO.getSaleCount() + skuOrderDTO.getQuantity().intValue());
        BigDecimal retureRate = new BigDecimal(indexDTO.getRetureCount()).multiply(new BigDecimal(100)).divide(new BigDecimal(indexDTO.getSaleCount()), 2, RoundingMode.HALF_UP);
        indexDTO.setRetureRate(retureRate.doubleValue());
    }

}