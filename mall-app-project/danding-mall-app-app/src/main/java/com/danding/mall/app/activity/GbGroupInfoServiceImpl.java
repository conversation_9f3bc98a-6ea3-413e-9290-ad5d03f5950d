package com.danding.mall.app.activity;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.danding.core.api.Result;
import com.danding.core.client.PageDTO;
import com.danding.core.client.SortEnum;
import com.danding.core.domain.utils.ConverterUtil;
import com.danding.core.redis.lock.RedissonDistributedLock;
import com.danding.mall.app.activity.api.IGbGroupInfoService;
import com.danding.mall.app.activity.api.IGbGroupTaskService;
import com.danding.mall.app.activity.dto.GbActivityConfigRewardQry;
import com.danding.mall.app.activity.dto.GbActivityInfoQry;
import com.danding.mall.app.activity.dto.GbGroupInfoQry;
import com.danding.mall.app.activity.dto.GbGroupMemberQry;
import com.danding.mall.app.activity.dto.data.*;
import com.danding.mall.app.activity.enums.*;
import com.danding.mall.app.activity.handler.GbGroupPageHandler;
import com.danding.mall.app.activity.handler.group.ActivityConfigRewardHandler;
import com.danding.mall.app.activity.handler.group.MemberOrderRefundHandler;
import com.danding.mall.app.activity.handler.price.GbActivityItemPriceCalculator;
import com.danding.mall.app.activity.vo.param.GbGroupCreateParam;
import com.danding.mall.app.activity.vo.param.GbGroupJoinParam;
import com.danding.mall.app.activity.vo.param.GbGroupOrderParam;
import com.danding.mall.app.activity.vo.request.GbGroupDetailReq;
import com.danding.mall.app.activity.vo.request.GbGroupInfoReq;
import com.danding.mall.app.activity.vo.request.GbGroupPageRequest;
import com.danding.mall.app.activity.vo.response.GbGroupInfoResp;
import com.danding.mall.app.activity.vo.response.GbGroupPageResponse;
import com.danding.mall.app.activity.vo.response.ItemResp;
import com.danding.mall.app.base.session.CommonUser;
import com.danding.mall.app.base.session.helper.ShopHelper;
import com.danding.mall.app.base.session.helper.UserHelper;
import com.danding.mall.app.base.utils.ImageUrlProcessor;
import com.danding.mall.app.coupon.api.ICouponActivityService;
import com.danding.mall.app.coupon.dto.bo.CouponActivityBO;
import com.danding.mall.app.domain.activity.gateway.*;
import com.danding.mall.app.domain.coupon.gateway.ICouponActivityGateway;
import com.danding.mall.app.domain.items.gateway.IItemDetailsGateway;
import com.danding.mall.app.domain.items.gateway.IItemsGateway;
import com.danding.mall.app.domain.order.gateway.IShopOrdersGateway;
import com.danding.mall.app.domain.user.gateway.IFamilyGroupMemberGateway;
import com.danding.mall.app.domain.user.gateway.IFamilyUserGateway;
import com.danding.mall.app.inventory.enums.InventoryTypeEnum;
import com.danding.mall.app.inventory.template.InventoryOperationTemplate;
import com.danding.mall.app.inventory.template.InventoryOperationTemplateSelector;
import com.danding.mall.app.items.dto.ItemsQry;
import com.danding.mall.app.items.dto.data.ItemDetailsDTO;
import com.danding.mall.app.items.dto.data.ItemsDTO;
import com.danding.mall.app.items.enums.ItemStatusEnum;
import com.danding.mall.app.items.utils.ActivitySkuPriceRangeResult;
import com.danding.mall.app.items.utils.SkuPriceRangeResult;
import com.danding.mall.app.items.utils.SkuPriceRangeUtil;
import com.danding.mall.app.order.dto.data.ShopOrderDTO;
import com.danding.mall.app.order.enums.OrderStatus;
import com.danding.mall.app.user.dto.data.FamilyUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 开团的团信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Service
public class GbGroupInfoServiceImpl implements IGbGroupInfoService {

    @Resource
    private IGbGroupInfoGateway gbGroupInfoGateway;

    @Resource
    private IGbGroupMemberGateway gbGroupMemberGateway;

    @Resource
    private IGbActivityInfoGateway gbActivityInfoGateway;

    @Resource
    private RedissonDistributedLock lock;

    @Resource
    private GbGroupInfoServiceImpl self;

    @Resource
    private IFamilyGroupMemberGateway familyGroupMemberGateway;

    @Resource
    private IFamilyUserGateway familyUserGateway;

    @Resource
    private IGbActivityConfigSkuGateway gbActivityConfigSkuGateway;

    @Resource
    private IItemsGateway itemsGateway;

    @Resource
    private IItemDetailsGateway itemDetailsGateway;

    @Resource
    private IGbActivityConfigExtendGateway gbActivityConfigExtendGateway;

    @Resource
    private InventoryOperationTemplateSelector inventoryOperationTemplateSelector;

    @Resource
    private GbGroupPageHandler gbGroupPageHandler;

    @Resource
    private IShopOrdersGateway shopOrdersGateway;

    @Resource
    private GbActivityItemPriceCalculator gbActivityItemPriceCalculator;

    @Resource
    private ActivityConfigRewardHandler activityConfigRewardHandler;

    @Resource
    private IGbActivityConfigRewardGateway gbActivityConfigRewardGateway;

    @Resource
    private ICouponActivityGateway couponActivityGateway;

    @Resource
    private ICouponActivityService couponActivityService;

    @Resource
    private MemberOrderRefundHandler memberOrderRefundHandler;

    @Resource
    private ImageUrlProcessor imageUrlProcessor;

    @Override
    public List<GbGroupInfoDTO> myGroup(GbGroupDetailReq gbGroupInfoReq) {
        CommonUser user = UserHelper.getUser();
        Long shopId = user.getShopId();
        Long userId = user.getId();
        // 根据用户ID 查询所参与到团的id
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setGroupUserId(userId);
        gbGroupMemberQry.setShopId(shopId);
        gbGroupMemberQry.setActivityId(gbGroupInfoReq.getActivityId());
        List<GbGroupMemberDTO> gbGroupMemberDTOS = gbGroupMemberGateway.selectList(gbGroupMemberQry);
        if (ObjectUtil.isEmpty(gbGroupMemberDTOS)) {
            return Collections.emptyList();
        }
        Set<Long> groupIdList = gbGroupMemberDTOS.stream()
                .map(GbGroupMemberDTO::getGroupId)   // 提取 getGroupId 字段
                .collect(Collectors.toSet());  // 收集

        //根据团ID 批量查询出来有那些。
        GbGroupInfoQry param = new GbGroupInfoQry();
        LinkedHashMap<String, SortEnum> sortParamMap = new LinkedHashMap<>();
        sortParamMap.put("group_time", SortEnum.DESC);
        param.setSortParamMap(sortParamMap);
        param.setIdList(new ArrayList<>(groupIdList));
        param.setShopId(shopId);
        List<GbGroupInfoDTO> gbGroupInfoDTOS = gbGroupInfoGateway.selectList(param);
        //补充差几人的信息。开团状态：1：进行中

        // 1. 提前加载所有需要的活动信息 activityId，减少多次查询
        Set<Long> activityIds = gbGroupInfoDTOS.stream()
                .map(GbGroupInfoDTO::getActivityId)
                .collect(Collectors.toSet());

        // 2. 批量查询活动信息（假设网关支持批量查询）
        GbActivityInfoQry gbActivityInfoQry = new GbActivityInfoQry();
        gbActivityInfoQry.setIdList(new ArrayList<>(activityIds));
        gbActivityInfoQry.setShopId(shopId);
        Map<Long, GbActivityInfoDTO> activityInfoMap = gbActivityInfoGateway.selectList(gbActivityInfoQry)
                .stream()
                .collect(Collectors.toMap(GbActivityInfoDTO::getId, Function.identity()));

        // 3. 并行处理
        gbGroupInfoDTOS.parallelStream()  // 并行流加速
                .filter(GbGroupInfoDTO::getInProgressGroupStatus)
                .forEach(dto -> {
                    GbGroupMemberQry qry = new GbGroupMemberQry();
                    qry.setGroupId(dto.getId());
                    qry.setShopId(shopId);
                    qry.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
                    Integer count = gbGroupMemberGateway.count(qry).intValue();
                    GbActivityInfoDTO activityInfo = activityInfoMap.get(dto.getActivityId());
                    if (activityInfo != null) {
                        dto.setActivityDes(activityInfo.getActivityDes());
                        dto.setLackMembers(activityInfo.getActivityNum() - count);
                    } else {
                        dto.setLackMembers(0); // 默认值
                    }
                });
        // 设置订单信息
        gbGroupInfoDTOS.stream().forEach(dto -> {
            GbGroupMemberQry gbGroupMemberQry1 = new GbGroupMemberQry();
            gbGroupMemberQry1.setGroupId(dto.getId());
            gbGroupMemberQry1.setGroupUserId(userId);
            gbGroupMemberQry1.setShopId(shopId);
            LinkedHashMap<String, SortEnum> sortMap = new LinkedHashMap<>();
            sortMap.put("join_time", SortEnum.DESC);
            gbGroupMemberQry1.setSortParamMap(sortMap);
            List<GbGroupMemberDTO> gbGroupMemberDTOS1 = gbGroupMemberGateway.selectList(gbGroupMemberQry1);
            GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberDTOS1.get(0);
            dto.setOrderId(gbGroupMemberDTO.getOrderId());

            ShopOrderDTO shopOrderDTO = shopOrdersGateway.selectById(gbGroupMemberDTO.getOrderId());
            if(shopOrderDTO != null){
                dto.setOrderStatus(shopOrderDTO.getStatus());
                Integer orderStatus = shopOrderDTO.getStatus();
                if(orderStatus != null){
                    OrderStatus orderStatusEnum = OrderStatus.fromInt(orderStatus);
                    if(OrderStatus.PAID.equals(orderStatusEnum)){
                        dto.setOrderStatusDesc("待发货");
                    }else{
                        dto.setOrderStatusDesc(orderStatusEnum.getDesc());
                    }
                }
            }
        });
        return gbGroupInfoDTOS;
    }

    /**
     * 1. 未登录，获取活动匿名信息
     * 2. 已经登记，获取活动实名信息
     * 2.1 实名信息-有团ID
     * 2.2 实名信息-无团ID
     * 3. 分享购特殊判断
     * @param gbGroupInfoReq 初始参数
     * @return 详情返回体
     */
    @Override
    public GbGroupInfoResp getGroupDetail(GbGroupDetailReq gbGroupInfoReq) {
        // 1. 初始化匿名信息
        GbGroupInfoResp resp = new GbGroupInfoResp();
        GbGroupInfoResp.anonymousInfo anonymousInfo = getAnonymousInfo(gbGroupInfoReq.getActivityId(), resp);
        resp.setAnonymousInfo(anonymousInfo);
        // 2. 若用户未登录，直接返回匿名信息
        CommonUser user = UserHelper.getUser();
        if (user == null || user.getId().equals(-1L)) {
            resp.setGroupActionButton(GbGroupActionEnum.NOT_LOGGED_IN.getCode());
            return resp;
        }
        //3. 获取实名信息部分
        if(gbGroupInfoReq.getGroupId() == null){
            // 3.1 不包含团ID，获取当前人开团信息
            Pair<GbGroupInfoDTO, GbGroupMemberDTO> ownGroup = getOwnGroup(gbGroupInfoReq.getActivityId());
            Integer memberStatus = getMemberStatus(ownGroup);
            resp.setGroupActionButton(memberStatus);
            if(ownGroup.getKey()!=null) {
                resp.getRealNameInfo().setGroupInfo(ownGroup.getKey());
                Pair<List<GbGroupInfoResp.realNameInfo.GroupMember>, Integer> memberAvatars = getMemberAvatars(ownGroup.getKey().getId(),resp.getAnonymousInfo().getActivityInfo().getMarketingToolId());
                resp.getRealNameInfo().setMemberAvatars(memberAvatars.getKey());
                resp.getRealNameInfo().setJoinedMemberCount(memberAvatars.getValue());
                resp.getRealNameInfo().setLackMembers(anonymousInfo.getActivityInfo().getActivityNum() - memberAvatars.getValue());
                resp.getRealNameInfo().setActivityNum(anonymousInfo.getActivityInfo().getActivityNum());
            }
            setOrderInfo(ownGroup, resp);
        }else{
            // 3.2 包含团ID，获取当前团信息
            Pair<GbGroupInfoDTO, GbGroupMemberDTO> theGroup = getTheGroup(gbGroupInfoReq.getGroupId());
            // 3.2.1 若为分享购且当前人员非该团团员，判断当前人是否为该团邀请，若邀请失败,则当前状态为新用户参团失败
            if ( resp.getAnonymousInfo().getActivityInfo().getMarketingToolId().equals(GbMarketingToolEnum.SHARE_BUY.getCode()) && theGroup.getValue() == null ) {
                FamilyUserDTO familyUserDTO = familyUserGateway.selectFamilyUser(UserHelper.getShopId(), UserHelper.getUserId());
                if(gbGroupInfoReq.getGroupId().equals(familyUserDTO.getGroupId())){
                    resp.setGroupActionButton(GbGroupActionEnum.NEW_MEMBER_FAIL.getCode());
                }else{
                    resp.setGroupActionButton(GbGroupActionEnum.OLD_MEMBER_FAIL.getCode());
                }
                return resp;
            }
            resp.getRealNameInfo().setGroupInfo(theGroup.getKey());
            resp.setGroupActionButton(getMemberStatus(theGroup));
            if(theGroup.getKey()!=null) {
                resp.getRealNameInfo().setGroupInfo(theGroup.getKey());
                Pair<List<GbGroupInfoResp.realNameInfo.GroupMember>, Integer> memberAvatars =getMemberAvatars(theGroup.getKey().getId(),resp.getAnonymousInfo().getActivityInfo().getMarketingToolId());
                resp.getRealNameInfo().setMemberAvatars(memberAvatars.getKey());
                resp.getRealNameInfo().setJoinedMemberCount(memberAvatars.getValue());
                resp.getRealNameInfo().setLackMembers(anonymousInfo.getActivityInfo().getActivityNum() - memberAvatars.getValue());
                resp.getRealNameInfo().setActivityNum(anonymousInfo.getActivityInfo().getActivityNum());
            }
            setOrderInfo(theGroup, resp);
            setDeadLine(theGroup, resp);
            // 3.2.2 刷新奖励信息
            List<GbActivityConfigRewardDTO> gbActivityConfigRewardDTOS = setRewardList(theGroup.getKey().getActivityId(), resp);
            resp.getAnonymousInfo().setGbActivityConfigRewardDTOS(gbActivityConfigRewardDTOS);
        }

        return resp;

    }

    /**
     * 未登录用户获取活动的匿名信息
     * @param activityId 活动ID
     * @param resp 初始返回体
     * @return
     */
    private  GbGroupInfoResp.anonymousInfo getAnonymousInfo(Long activityId,GbGroupInfoResp resp){
        GbGroupInfoResp.anonymousInfo anonymousInfo = new GbGroupInfoResp.anonymousInfo();
        // 1. 获取匿名部分信息
        // 1.1 获取活动信息
        // 1.1.1 基本信息
        GbActivityInfoDTO gbActivityInfoDTO = gbActivityInfoGateway.selectById(activityId);
        // 1.1.2 获取活动商品总数
        Long skusSum = gbActivityConfigSkuGateway.getSkusSum(activityId);
        // 1.1.3 获取活动成团底数
        String initGroupCount = gbActivityConfigExtendGateway.getExtendValue(activityId, "init_group_count");
        // 1.2 校验活动有效性
        checkActivityStatus(gbActivityInfoDTO);
        // 1.3 获取团数量，查询团列表，状态：进行中，开团成功
        List<GbGroupInfoDTO> groupInfoList = queryGroupInfoList(activityId);
        // 1.4 获取妈妈数量
        Integer motherCount= getMotherCount(gbActivityInfoDTO);
        // 1.5 获取成团人数
        Integer successCount = getSuccessCount(gbActivityInfoDTO);
        // 1.6 获取滚动条信息
        Pair<List<GbGroupInfoResp.UserScrollItem>, Integer> listIntegerPair = setScrollItems(gbActivityInfoDTO,groupInfoList);
        // 1.7 获取spu清单
        List<Long> items = new ArrayList<>();
        List<GbActivityConfigSkuDTO> gbActivityConfigSkuDTOS = gbActivityConfigSkuGateway.selectListByActivityId(gbActivityInfoDTO.getId());
        for (GbActivityConfigSkuDTO gbActivityConfigSkuDTO : gbActivityConfigSkuDTOS) {
            items.add(gbActivityConfigSkuDTO.getItemId());
        }
        Map<Long, List<GbActivityConfigSkuDTO>> activityConfigSkuMap = gbActivityConfigSkuDTOS.stream().collect(Collectors.groupingBy(GbActivityConfigSkuDTO::getItemId));

        // 仅返回上架的商品
        ItemsQry itemsQry = new ItemsQry();
        itemsQry.setItemIds(items);
        itemsQry.setStatus(ItemStatusEnum.ON_SHELF.getCode());
        List<ItemsDTO> itemsDTOS = itemsGateway.selectList(itemsQry);

        //1.8 统一封装匿名数据
        anonymousInfo.setActivityInfo(gbActivityInfoDTO);
        anonymousInfo.setGroupActivityCount(groupInfoList.size()+Integer.parseInt(initGroupCount));
        anonymousInfo.setMotherCount(motherCount);

        //1.9 分享购-获取优惠卷数据
        //1.9.1 获取默认奖励列表
        List<GbActivityConfigRewardDTO> gbActivityConfigRewardDTOS = setRewardList(gbActivityInfoDTO.getId(), resp);

        List<ItemResp> itemList = itemsDTOS.stream().map(dto -> {
            ItemResp itemResp = ConverterUtil.convert(dto, ItemResp.class);
            ItemDetailsDTO detailsDTO = itemDetailsGateway.selectByItemId(dto.getId());
            if (detailsDTO != null){
                // TODO 要确认返回的格式
                if(StringUtils.isNotEmpty(dto.getMainImage())){
                    itemResp.setMainImage(imageUrlProcessor.restore(dto.getMainImage()));
                }
                if(StringUtils.isNotEmpty(detailsDTO.getDetailJson())){
                    List<String> imageList = JSONObject.parseArray(detailsDTO.getDetailJson(), String.class);
                    List<String> lastImageList = imageList.stream().map(img -> imageUrlProcessor.restore(img)).collect(Collectors.toList());
                    itemResp.setDetailImageList(lastImageList);
                }
            }
            List<GbActivityConfigSkuDTO> configSkuList = activityConfigSkuMap.get(dto.getId());

            ActivitySkuPriceRangeResult priceRangeResult = SkuPriceRangeUtil.findActivitySkuPriceRange(configSkuList);
            Integer minPrice = priceRangeResult.getMinPrice();
            Integer maxPrice = priceRangeResult.getMaxPrice();

            itemResp.setItemHighPrice(new BigDecimal(maxPrice).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            itemResp.setItemLowPrice(new BigDecimal(minPrice).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            return itemResp;
        }).collect(Collectors.toList());

        anonymousInfo.setItemsDTOS(itemList);
        anonymousInfo.setScrollItems(listIntegerPair.getKey());
        anonymousInfo.setSuccessGroupCount(successCount);
        anonymousInfo.setTotalProducts(skusSum.intValue());
        anonymousInfo.setGbActivityConfigRewardDTOS(gbActivityConfigRewardDTOS);

        return anonymousInfo;
    }
    /**
     * 获取奖励列表
     * @param activityId 活动Id
     */
    private List<GbActivityConfigRewardDTO> setRewardList(Long activityId, GbGroupInfoResp resp){
        GbActivityConfigRewardQry rewardQry = new GbActivityConfigRewardQry();
        rewardQry.setActivityId(activityId);
        // 若是匿名信息获取团长奖励，实名信息获取当前用户角色奖励
        if(resp.getRealNameInfo().getMemberRole() == null){
            rewardQry.setRewardRange(GbRewardRangeEnum.LEADER.getCode());
        }else{
            rewardQry.setRewardRange(resp.getRealNameInfo().getMemberRole());
        }
        List<GbActivityConfigRewardDTO> gbActivityConfigRewardDTOS = gbActivityConfigRewardGateway.selectList(rewardQry);
        gbActivityConfigRewardDTOS.forEach(gbActivityConfigRewardDTO->{
            if(gbActivityConfigRewardDTO.getCouponFlag().equals(1)&&StringUtils.isNotEmpty(gbActivityConfigRewardDTO.getCouponIds())){
                // 1.9.2 如果是优惠卷获取优惠卷详情
                List<Long> cphList = Arrays.stream(gbActivityConfigRewardDTO.getCouponIds().split(";"))
                        .filter(s -> !s.trim().isEmpty())
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                List<CouponActivityBO> couponActivityBOS = couponActivityService.listById(cphList);
                gbActivityConfigRewardDTO.setCouponActivityDTOs(couponActivityBOS);
            }
        });
        return gbActivityConfigRewardDTOS;
    }

    private void setOrderInfo(Pair<GbGroupInfoDTO, GbGroupMemberDTO> theGroup, GbGroupInfoResp resp) {
        GbGroupMemberDTO gbGroupMemberDTO = theGroup.getValue();
        if(gbGroupMemberDTO != null){
            Long shopOrderId = gbGroupMemberDTO.getOrderId();
            resp.getRealNameInfo().setMemberRole(gbGroupMemberDTO.getMemberRole());
            if(shopOrderId != null){
                ShopOrderDTO shopOrder = shopOrdersGateway.selectById(shopOrderId);
                if(shopOrder != null){
                    resp.getRealNameInfo().setOrderId(shopOrder.getId());
                    resp.getRealNameInfo().setOrderStatus(shopOrder.getStatus());
                    Integer orderStatus = shopOrder.getStatus();
                    if(orderStatus != null){
                        resp.getRealNameInfo().setOrderStatus(shopOrder.getStatus());
                        OrderStatus orderStatusEnum = OrderStatus.fromInt(orderStatus);
                        if(OrderStatus.PAID.equals(orderStatusEnum)){
                            resp.getRealNameInfo().setOrderStatusDesc("待发货");
                        }else{
                            resp.getRealNameInfo().setOrderStatusDesc(orderStatusEnum.getDesc());
                        }
                    }
                }
            }else{
                log.info("无订单ID");
            }

        }

    }

    /**
     * 获取支付时限
     */
    private void setDeadLine(Pair<GbGroupInfoDTO, GbGroupMemberDTO> theGroup,GbGroupInfoResp resp){
        GbActivityInfoDTO activityInfo = resp.getAnonymousInfo().getActivityInfo();
        GbGroupMemberDTO gbGroupMemberDTO = theGroup.getValue();
        if(activityInfo.getMarketingToolId().equals(GbMarketingToolEnum.SHARE_BUY.getCode())){
            //计算时限
            long l = (long) (activityInfo.getPaymentDeadline() * 60 * 1000) + gbGroupMemberDTO.getJoinTime() -System.currentTimeMillis() ;
            resp.getRealNameInfo().setPaymentDeadline(l);
        }
    }

    private void checkActivityStatus(GbActivityInfoDTO gbActivityInfoDTO){
        if(!gbActivityInfoDTO.getActivityStatus().equals(GbActivityStatusEnum.IN_PROGRESS.getCode())){
            throw new RuntimeException("该团活动已结束，不能再继续参与了");
        }
    }

    private List<GbGroupInfoDTO> queryGroupInfoList(Long activityId) {
        GbGroupInfoQry qry = new GbGroupInfoQry();
        qry.setShopId(UserHelper.getShopId());
        qry.setActivityId(activityId);
        qry.setGroupStatusList(Arrays.asList(
                GbGroupStatusEnum.IN_PROGRESS.getCode(),
                GbGroupStatusEnum.GROUP_SUCCESS.getCode(),
                GbGroupStatusEnum.GROUP_FULL.getCode()
        ));
        LinkedHashMap<String, SortEnum> sortParamMap = new LinkedHashMap<>();
        sortParamMap.put("group_time", SortEnum.DESC);
        qry.setSortParamMap(sortParamMap);
        return gbGroupInfoGateway.selectList(qry);
    }

    private Integer getMotherCount(GbActivityInfoDTO gbActivityInfoDTO) {
        // 获取团员统计信息，团员状态：在团、开团成功
        GbGroupMemberQry memberQry = new GbGroupMemberQry();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        memberQry.setStatusList(statusList);
        memberQry.setActivityId(gbActivityInfoDTO.getId());
        Long count = gbGroupMemberGateway.count(memberQry);
        return count.intValue();
    }

    private Integer getSuccessCount(GbActivityInfoDTO gbActivityInfoDTO) {
        // 获取团员统计信息，团员状态：开团成功
        GbGroupMemberQry memberQry = new GbGroupMemberQry();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        memberQry.setStatusList(statusList);
        memberQry.setActivityId(gbActivityInfoDTO.getId());
        Long count = gbGroupMemberGateway.count(memberQry);
        return count.intValue();
    }

    private Pair<List<GbGroupInfoResp.UserScrollItem>,Integer> setScrollItems(GbActivityInfoDTO gbActivityInfoDTO,List<GbGroupInfoDTO> groupInfoList) {
        List<GbGroupInfoResp.UserScrollItem> scrollItems = new ArrayList<>();
        int successGroupCount = 0;

        GbGroupMemberQry memberQry = new GbGroupMemberQry();
        memberQry.setShopId(UserHelper.getShopId());

        for (GbGroupInfoDTO groupInfo : groupInfoList) {
            List<GbGroupInfoResp.UserScrollItem> items = new ArrayList<>();
            memberQry.setActivityId(groupInfo.getActivityId());
            memberQry.setGroupId(groupInfo.getId());
            if (groupInfo.getGroupSuccessGroupStatus()) {
                // 处理成功团
                items = handleGroup(groupInfo, memberQry);
                successGroupCount++;
            } else {
                // 处理普通团
                items = handleGroup(groupInfo, memberQry);
            }

            scrollItems.addAll(items);
        }

        return  new Pair<>(scrollItems, successGroupCount);
    }

    //成功的团获取团长开团成功和团员拼团成功
    private List<GbGroupInfoResp.UserScrollItem> handleGroup(GbGroupInfoDTO groupInfo,GbGroupMemberQry memberQry) {
        List<GbGroupInfoResp.UserScrollItem> scrollItems = new ArrayList<>();
        LinkedHashMap<String, SortEnum> sortParamMap = new LinkedHashMap<>();
        //顺序排序，首个为团长
        sortParamMap.put("join_time", SortEnum.ASC);
        memberQry.setSortParamMap(sortParamMap);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        memberQry.setStatusList(statusList);
        List<GbGroupMemberDTO> members = gbGroupMemberGateway.selectList(memberQry);
        if (!members.isEmpty()) {
            // 获取团长信息
            GbGroupInfoResp.UserScrollItem leader = new GbGroupInfoResp.UserScrollItem();
            setUserInfo(leader, ShopHelper.getId(), members.get(0).getGroupUserId());
            leader.setJoinTime(members.get(0).getJoinTime());
            leader.setGroupStatus(1);
            scrollItems.add(leader);
            if(members.size() > 1) {
                // 获取末尾团员信息
                if(groupInfo.getGroupStatus().equals(GbGroupStatusEnum.GROUP_SUCCESS.getCode())) {
                    GbGroupInfoResp.UserScrollItem member = new GbGroupInfoResp.UserScrollItem();
                    setUserInfo(member, ShopHelper.getId(), members.get(members.size()-1).getGroupUserId());
                    member.setGroupStatus(3);
                    member.setJoinTime( members.get(members.size()-1).getJoinTime());
                    scrollItems.add(member);
                }
//                }else{
//                    member.setGroupStatus(2);
//                }
            }
        }
        return scrollItems;
    }

    private void setUserInfo(GbGroupInfoResp.UserScrollItem item,
                             Long shopId, Long userId) {
        String groupName = familyGroupMemberGateway.findLastGroupName(shopId, userId);
        FamilyUserDTO user = familyUserGateway.selectFamilyUser(shopId, userId);

        if (user != null) {
            item.setUserAvatar(user.getHeadImg());
            item.setNickname(user.getNickName());
        }
        item.setUserGroup(groupName);
    }

    //无ID传入，当前人员在当前活动下的开团信息,状态：待支付、进行中、开团成功、已发货、已签收
    private Pair<GbGroupInfoDTO,GbGroupMemberDTO> getOwnGroup(Long activityId){
        GbGroupMemberQry memberQry = new GbGroupMemberQry();
        memberQry.setActivityId(activityId);
        memberQry.setGroupUserId(UserHelper.getUserId());
        memberQry.setMemberRole(GbGroupMemberRoleEnum.MEMBER_ROLE_LEADER.getCode());
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        memberQry.setStatusList(statusList);
        List<GbGroupMemberDTO> members = gbGroupMemberGateway.selectList(memberQry);
        if (members.isEmpty()) {
            return Pair.of(null, null);
        }else{
            GbGroupMemberDTO gbGroupMemberDTO = members.get(0);
            GbGroupInfoQry gbGroupInfoQry = new GbGroupInfoQry();
            gbGroupInfoQry.setId(gbGroupMemberDTO.getGroupId());
            gbGroupInfoQry.setShopId(UserHelper.getShopId());
            GbGroupInfoDTO gbGroupInfoDTO = gbGroupInfoGateway.selectOne(gbGroupInfoQry);
            return new Pair<>(gbGroupInfoDTO,gbGroupMemberDTO);
        }
    }
    //有ID传入，当前团信息和团成员信息,状态：待支付、进行中、开团成功、已发货、已签收
    private Pair<GbGroupInfoDTO,GbGroupMemberDTO> getTheGroup(Long groupId){
        GbGroupInfoDTO gbGroupInfoDTO = gbGroupInfoGateway.selectById(groupId);
        GbGroupMemberQry memberQry = new GbGroupMemberQry();
        memberQry.setGroupUserId(UserHelper.getUserId());
        memberQry.setGroupId(groupId);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        memberQry.setStatusList(statusList);
        List<GbGroupMemberDTO> members = gbGroupMemberGateway.selectList(memberQry);
        if (members.isEmpty()) {
            return new Pair<>(gbGroupInfoDTO,null);
        }else{
            return new Pair<>(gbGroupInfoDTO, members.get(0));
        }
    }
    //四层状态，一、有没有团信息 二、有没有团员信息 三、团状态如何 四、团员状态如何
    private Integer getMemberStatus(Pair<GbGroupInfoDTO, GbGroupMemberDTO> groupAndMember) {
        GbGroupInfoDTO gbGroupInfoDTO = groupAndMember.getKey();
        GbGroupMemberDTO gbGroupMemberDTO = groupAndMember.getValue();
        // 0. 判断是否有团
        if(Objects.isNull(gbGroupInfoDTO)){
            //1.1 没有团信息，即：该人员未开团，也未参团
            return GbGroupActionEnum.NON_MEMBER_WAITING_TO_START.getCode();
        }else{
            // 1.2.1 有团信息，无本人团员信息，即：该人员进入其他人的团
            GbGroupStatusEnum status = GbGroupStatusEnum.fromCode(gbGroupInfoDTO.getGroupStatus());
            if(Objects.isNull(gbGroupMemberDTO)) {
                switch (status) {
                    // 非团员-团进行中
                    case IN_PROGRESS:
                        return GbGroupActionEnum.NON_MEMBER_WAITING_TO_JOIN.getCode();
                    // 非团员-团员已满
                    case GROUP_FULL:
                        return GbGroupActionEnum.NON_MEMBER_GROUP_FULL.getCode();
                    // 非团员-开团成功
                    case GROUP_SUCCESS:
                        return GbGroupActionEnum.NON_MEMBER_GROUP_SUCCESS.getCode();
                    // 非团员-开团失败
                    case GROUP_FAILED:
                        return GbGroupActionEnum.NON_MEMBER_GROUP_FAIL.getCode();
                }
            }else{
                //1.2.1 有团信息，有本人团员信息，即：团员进入自有团
                // 1.2.1.1 团是否成功
                switch (status) {
                    // 团员-开团成功
                    case GROUP_SUCCESS:
                        return GbGroupActionEnum.MEMBER_SUCCESS.getCode();
                    // 团员-开团失败
                    case GROUP_FAILED:
                        return GbGroupActionEnum.MEMBER_GROUP_FAIL.getCode();
                    // 团员-团员已满
                    case GROUP_FULL:
                        //优先显示代付款、代下单
                        if(gbGroupMemberDTO.getMemberStatus().equals(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode())){
                            return GbGroupActionEnum.MEMBER_GROUP_OCCUPANCY.getCode();
                        }
                        if(gbGroupMemberDTO.getMemberStatus().equals(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode())){
                            return GbGroupActionEnum.MEMBER_NOT_PAID.getCode();
                        }
                        return GbGroupActionEnum.MEMBER_GROUP_FULL.getCode();
                }
                // 1.2.1.2 创建或者进行中的团，判断团员状态
                GbGroupMemberStatusEnum memberStatus = GbGroupMemberStatusEnum.fromCode(gbGroupMemberDTO.getMemberStatus());
                switch (memberStatus) {
                    // 团员未支付
                    case MEMBER_STATUS_BEFORE_PAY:
                        return GbGroupActionEnum.MEMBER_NOT_PAID.getCode();
                    // 团员-在团
                    case MEMBER_STATUS_IN:
                        return GbGroupActionEnum.MEMBER_IN_PROGRESS.getCode();
                    // 团员-占位待下单
                    case MEMBER_STATUS_OCCUPANCY:
                        return GbGroupActionEnum.MEMBER_GROUP_OCCUPANCY.getCode();
                }
            }

        }
        return GbGroupActionEnum.NON_MEMBER_WAITING_TO_START.getCode();
    }

    private @NotNull Optional<GbGroupMemberDTO> getGbGroupMemberDTO(GbGroupInfoReq req, GbGroupMemberQry qry) {
        LinkedHashMap<String, SortEnum> sortParamMap = new LinkedHashMap<>();
        sortParamMap.put("join_time", SortEnum.DESC);
        qry.setSortParamMap(sortParamMap);
        List<GbGroupMemberDTO> members = gbGroupMemberGateway.selectList(qry);
        long count = members.stream()
                .filter(dto -> Objects.equals(dto.getMemberStatus(), GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode()))  // 假设状态是int类型
                .count();
        req.setLackMembers((int) count);
        // 查找当前用户在团中的记录
        Optional<GbGroupMemberDTO> userRecord = members.stream()
                .filter(dto -> Objects.equals(dto.getGroupUserId(), req.getUserId()))
                .findFirst();
        return userRecord;
    }

    private Pair<List<GbGroupInfoResp.realNameInfo.GroupMember>,Integer> getMemberAvatars(Long groupId,Integer marketTool) {
        GbGroupMemberQry qry = new GbGroupMemberQry();
        qry.setGroupId(groupId);
        qry.setShopId(UserHelper.getShopId());
        // 查询团员记录：待下单、待支付、已支付、开团成功、已发货、已签收
        List<Integer> memberList = new ArrayList<>();
        if(Objects.equals(marketTool, GbMarketingToolEnum.SHARE_BUY.getCode())){
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        }else{
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
            memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        }
        qry.setStatusList(memberList);
        List<GbGroupMemberDTO> members = gbGroupMemberGateway.selectList(qry);
        return setHeadImg(members);
    }

    private Pair<List<GbGroupInfoResp.realNameInfo.GroupMember>, Integer> setHeadImg(List<GbGroupMemberDTO> members) {
        List<GbGroupInfoResp.realNameInfo.GroupMember> avatars = members.stream()
                .map(m -> {
                    FamilyUserDTO user = familyUserGateway.selectFamilyUser(UserHelper.getShopId(), m.getGroupUserId());
                    GbGroupInfoResp.realNameInfo.GroupMember groupMember = new GbGroupInfoResp.realNameInfo.GroupMember();
                    groupMember.setImages(user != null ? user.getHeadImg() : null);
                    groupMember.setStatus(m.getMemberStatus());
                    groupMember.setRole(m.getMemberRole());
                    return groupMember;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 统一分享购和拼团购页面显示逻辑
        int count = 0;
        List<Integer> memberList = new ArrayList<>();
        memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        memberList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        for(GbGroupMemberDTO memberDTO: members){
            if(memberList.contains(memberDTO.getMemberStatus())) {
                count++;
            }
        }
        return new Pair<>(avatars,count);
    }


    @Override
    public Long beforePayCreate(GbGroupCreateParam gbGroupCreateParam) {
        //0. 获取活动锁，防止活动状态变更入团
        String lockKey = IGbGroupTaskService.groupIdLockKey(gbGroupCreateParam.getActivityId());
        AtomicReference<Long> groupId = new AtomicReference<>();
        lock.lockNotReturn(lockKey, () -> groupId.set(self.handleCreate(gbGroupCreateParam)));
        return groupId.get();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long handleCreate(GbGroupCreateParam gbGroupCreateParam) {
        //1. 获取活动状态，若活动失效则不能开团
        GbActivityInfoDTO gbActivityInfoDTO = gbActivityInfoGateway.selectById(gbGroupCreateParam.getActivityId());
        if (!gbActivityInfoDTO.getActivityStatus().equals(GbActivityStatusEnum.IN_PROGRESS.getCode())) {
            throw new RuntimeException("活动已结束,不能再继续参与了");
        }
        //todo 更强的验证需要进行时间判断，防止活动自动更新的延迟
        //2. 验证当前用户在该活动中是否开过团，验证方式：活动-团-团员信息中是否包含该部分团员，且状态为：待支付、在团、开团成功
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setActivityId(gbGroupCreateParam.getActivityId());
        gbGroupMemberQry.setGroupUserId(UserHelper.getUserId());
        gbGroupMemberQry.setShopId(UserHelper.getShopId());
        // 团长角色
        gbGroupMemberQry.setMemberRole(GbGroupMemberRoleEnum.MEMBER_ROLE_LEADER.getCode());
        // 封装状态集合
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        gbGroupMemberQry.setStatusList(statusList);
        Long count = gbGroupMemberGateway.count(gbGroupMemberQry);
        if (count > 0) {
            throw new RuntimeException("当前用户在该活动中已经开过团了，请勿重复开团！");
        }
        //3. 创建待支付团
        return gbGroupInfoGateway.leaderCreate(gbActivityInfoDTO.getShopId(), gbGroupCreateParam.getActivityId(), gbGroupCreateParam.getOrderId());
    }


    @Override
    public void memberPay(GbGroupOrderParam gbGroupOrderParam) {
        //1. 按照订单活动团信息
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupOrderParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        //2. 获取团锁，在锁内进行事务操作
        String lockKey = IGbGroupInfoService.GroupLockKey(gbGroupMemberDTO.getGroupId());
        lock.lockNotReturn(lockKey, () -> self.memberPayOperate(gbGroupMemberDTO));
    }


    @Transactional(rollbackFor = Exception.class)
    public void memberPayOperate(GbGroupMemberDTO gbGroupMemberDTO) {
        Long activityId = gbGroupMemberDTO.getActivityId();
        Long shopId = gbGroupMemberDTO.getShopId();
        Long orderId = gbGroupMemberDTO.getOrderId();
        GbGroupInfoDTO gbGroupInfoDTO = gbGroupInfoGateway.selectById(gbGroupMemberDTO.getGroupId());
        //验证团状态，开团成功/失败的团需要进行退款,退款后要進行
        if(gbGroupInfoDTO.getGroupStatus().equals(GbGroupStatusEnum.GROUP_SUCCESS.getCode())||gbGroupInfoDTO.getGroupStatus().equals(GbGroupStatusEnum.GROUP_FAILED.getCode())) {
            log.info("当前团已经成功/失败，当前支付无效，触发团员退款！{}",gbGroupMemberDTO.getId());
            memberOrderRefundHandler.orderReturnBeforeIn(gbGroupMemberDTO);
            //该部分存在BUG，该团员会一直处于待支付状态
            return ;
        }
        // 1. 判断是团员还是团长
        if (gbGroupMemberDTO.getMemberRole().equals(GbGroupMemberRoleEnum.MEMBER_ROLE_LEADER.getCode())) {
            //1.1 团长支付
            gbGroupInfoGateway.leaderPay(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());

            // 2. 扣减团长支付库存
            InventoryOperationTemplate template = inventoryOperationTemplateSelector.getTemplate(gbGroupMemberDTO);
            template.orderPayment(orderId);

            // todo 3. 注意：团长支付后会存在团已到期的问题，这时候需要按照配置自动成团(定时任务方法)
            //  也就是说库存返还要在清空所有团员状态之后。
            /**
             * 未配置自动成团：
             *  1、
             */

        } else {
            //1.2 团员支付
            gbGroupInfoGateway.memberPay(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());

            // 2. 扣减团员支付库存
            InventoryOperationTemplate template = inventoryOperationTemplateSelector.getTemplate(gbGroupMemberDTO);
            template.orderPayment(orderId);
        }
    }


    @Override
    public Long memberJoin(GbGroupJoinParam gbGroupJoinParam) {
        //分布式获取团锁，并进行团员加入操作，防止加入过程中状态变更
        String lockKey = IGbGroupInfoService.GroupLockKey(gbGroupJoinParam.getGroupId());
        AtomicReference<Long> memberId = new AtomicReference<>();
        lock.lockNotReturn(lockKey, () -> memberId.set(self.memberJoinOperate(gbGroupJoinParam)));
        return memberId.get();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long memberJoinOperate(GbGroupJoinParam gbGroupJoinParam) {
        // 1. 团状态验证

        List<Integer> qryStatusList = new ArrayList<>();
        qryStatusList.add(GbGroupStatusEnum.IN_PROGRESS.getCode());
        qryStatusList.add(GbGroupStatusEnum.GROUP_FULL.getCode());

        GbGroupInfoDTO gbGroupInfoDTO = gbGroupInfoGateway.selectById(gbGroupJoinParam.getGroupId());
        if (!qryStatusList.contains(gbGroupInfoDTO.getGroupStatus())) {
            throw new RuntimeException("当前团已成团或者取消！");
        }
        //2. 团员状态验证,不能存在：待支付、在团、开团成功
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setGroupId(gbGroupJoinParam.getGroupId());
        gbGroupMemberQry.setGroupUserId(UserHelper.getUserId());
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        gbGroupMemberQry.setStatusList(statusList);
        Long count = gbGroupMemberGateway.count(gbGroupMemberQry);
        if (count > 0) {
            throw new RuntimeException("当前用户已在该团中！");
        }
        // 3. 判断是那种类型的活动
        GbActivityInfoDTO gbActivityInfoDTO = gbActivityInfoGateway.selectById(gbGroupJoinParam.getActivityId());
        GbMarketingToolEnum gbMarketingToolEnum = GbMarketingToolEnum.fromCode(gbActivityInfoDTO.getMarketingToolId());
        switch (gbMarketingToolEnum) {
            // 拼团购
            case GROUP_BUY:
                return gbGroupInfoGateway.memberIn(gbGroupJoinParam.getActivityId(), gbGroupJoinParam.getGroupId(), gbGroupJoinParam.getOrderId());
            // 分享购
            case SHARE_BUY:
                return gbGroupInfoGateway.memberOInAfterOccupancy(UserHelper.getShopId(),gbGroupJoinParam.getActivityId(), gbGroupJoinParam.getGroupId(), gbGroupJoinParam.getMemberId(),gbGroupJoinParam.getOrderId());
            default:
                throw new RuntimeException("当前营销类型不存在！");
        }
    }

    @Override
    public void MemberCancelPay(GbGroupOrderParam gbGroupOrderParam) {
        //1. 按照订单活动团信息
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupOrderParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        //理论上取消订单不影响其他人入/出团，但是为了保险起见，加入分布式锁，后续性能问题可进行删除
        String lockKey = IGbGroupInfoService.GroupLockKey(gbGroupMemberDTO.getGroupId());
        lock.lockNotReturn(lockKey, () -> self.memberOut(gbGroupMemberDTO));
    }

    @Override
    public void orderPaidExpired(GbGroupOrderParam gbGroupOrderParam) {
        //1. 按照订单活动团信息
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupOrderParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        //理论上订单超时关闭不影响其他人入/出团，但是为了保险起见，加入分布式锁，后续性能问题可进行删除
        String lockKey = IGbGroupInfoService.GroupLockKey(gbGroupMemberDTO.getGroupId());
        lock.lockNotReturn(lockKey, () -> self.memberOut(gbGroupMemberDTO));
    }

    @Transactional(rollbackFor = Exception.class)
    public void memberOut(GbGroupMemberDTO gbGroupMemberDTO) {
        if (gbGroupMemberDTO.getMemberRole().equals(GbGroupMemberRoleEnum.MEMBER_ROLE_LEADER.getCode())) {
            gbGroupInfoGateway.leaderCancelPay(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());
        } else {
            gbGroupInfoGateway.memberOut(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberQuit(GbGroupMemberDTO gbGroupMemberDTO) {
        gbGroupInfoGateway.memberOut(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());

    }

    @Override
    public void memberRefund(GbGroupOrderParam gbGroupOrderParam) {
        //1. 按照订单活动团信息
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupOrderParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        String lockKey = IGbGroupInfoService.GroupLockKey(gbGroupMemberDTO.getGroupId());
        lock.lockNotReturn(lockKey, () -> self.memberRefundOperate(gbGroupMemberDTO));
    }

    @Override
    public void memberShipped(GbGroupOrderParam gbGroupParam) {
        //1. 按照订单活动团信息
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        String lockKey = IGbGroupInfoService.GroupLockKey(gbGroupMemberDTO.getGroupId());
        lock.lockNotReturn(lockKey, () -> self.memberShippedOperate(gbGroupMemberDTO));
    }

    private void memberShippedOperate(GbGroupMemberDTO gbGroupMemberDTO) {
        log.info("团员信息 发货 {}", JSONObject.toJSONString(gbGroupMemberDTO));
        // 0. 验证当前团状态,只有开团成功的团退款才需要进行团操作
        GbGroupInfoDTO gbGroupInfoDTO = gbGroupInfoGateway.selectById(gbGroupMemberDTO.getGroupId());
        if (!gbGroupInfoDTO.getGroupStatus().equals(GbGroupStatusEnum.GROUP_SUCCESS.getCode())) {
            return;
        }
        gbGroupInfoGateway.memberShipped(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());
    }

    @Override
    public void memberSigned(GbGroupOrderParam gbGroupParam) {
        //1. 按照订单活动团信息
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        String lockKey = IGbGroupInfoService.GroupLockKey(gbGroupMemberDTO.getGroupId());
        lock.lockNotReturn(lockKey, () -> self.memberSignedOperate(gbGroupMemberDTO));
    }

    public void memberSignedOperate(GbGroupMemberDTO gbGroupMemberDTO) {
        log.info("团员信息 签收 {}", JSONObject.toJSONString(gbGroupMemberDTO));
        // 0. 验证当前团状态,只有开团成功的团退款才需要进行团操作
        GbGroupInfoDTO gbGroupInfoDTO = gbGroupInfoGateway.selectById(gbGroupMemberDTO.getGroupId());
        if (!gbGroupInfoDTO.getGroupStatus().equals(GbGroupStatusEnum.GROUP_SUCCESS.getCode())) {
            return;
        }
        gbGroupInfoGateway.memberSigned(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());

        // TODO 测试方便 签收即发放奖励
        activityConfigRewardHandler.memberReward(gbGroupMemberDTO);
    }

    @Override
    public Result<PageDTO<GbGroupPageResponse>> page(GbGroupPageRequest request) {
        return gbGroupPageHandler.page(request);
    }

    @Transactional(rollbackFor = Exception.class)
    public void memberRefundOperate(GbGroupMemberDTO gbGroupMemberDTO) {
        Long shopId = gbGroupMemberDTO.getShopId();
        Long orderId = gbGroupMemberDTO.getOrderId();

        // 0. 验证当前团状态,只有进行中的团退款才需要进行团操作
        GbGroupInfoDTO gbGroupInfoDTO = gbGroupInfoGateway.selectById(gbGroupMemberDTO.getGroupId());
        List<Integer> stausList = new ArrayList<>();
        stausList.add(GbGroupStatusEnum.GROUP_SUCCESS.getCode());
        if (stausList.contains(gbGroupInfoDTO.getGroupStatus())) {
            log.error("团状态：开团成功 忽略 {} {}", gbGroupMemberDTO.getGroupId(), orderId);
            return;
        }
        if (gbGroupMemberDTO.getMemberRole().equals(GbGroupMemberRoleEnum.MEMBER_ROLE_LEADER.getCode())) {

            //1. 团长退团
            gbGroupInfoGateway.LeaderRefund(gbGroupMemberDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());

            // 团长退款
            memberOrderRefundHandler.memberOrderReturnByGroup(gbGroupInfoDTO);
            // todo 3. 注意：团长支付后会存在团已到期的问题，这时候需要按照配置自动成团，也就是说库存返还要在清空所有团员状态之后。
        } else {
            // 1.2 团员退团
            gbGroupInfoGateway.memberRefund(gbGroupInfoDTO.getShopId(), gbGroupMemberDTO.getActivityId(), gbGroupMemberDTO.getGroupId(), gbGroupMemberDTO.getId());
        }
    }

    @Override
    public void memberDeliveryTime(GbGroupOrderParam gbGroupParam) {
        if (Objects.isNull(gbGroupParam.getOrderId()) || Objects.isNull(gbGroupParam.getDeliveryTime())) {
            return;
        }
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        if (Objects.nonNull(gbGroupMemberDTO)) {
            gbGroupMemberDTO.setDeliveryTime(gbGroupParam.getDeliveryTime());
            gbGroupMemberGateway.updateById(gbGroupMemberDTO.getId(), gbGroupMemberDTO);
        }
    }

    @Override
    public void memberRefundTime(GbGroupOrderParam gbGroupParam) {
        if (Objects.isNull(gbGroupParam.getOrderId()) || Objects.isNull(gbGroupParam.getRefundTime())) {
            return;
        }
        GbGroupMemberQry gbGroupMemberQry = new GbGroupMemberQry();
        gbGroupMemberQry.setOrderId(gbGroupParam.getOrderId());
        GbGroupMemberDTO gbGroupMemberDTO = gbGroupMemberGateway.selectOne(gbGroupMemberQry);
        if (Objects.nonNull(gbGroupMemberDTO)) {
            gbGroupMemberDTO.setRefundTime(gbGroupParam.getRefundTime());
            gbGroupMemberGateway.updateById(gbGroupMemberDTO.getId(), gbGroupMemberDTO);
        }
    }

}