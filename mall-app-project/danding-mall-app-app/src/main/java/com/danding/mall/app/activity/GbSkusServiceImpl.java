package com.danding.mall.app.activity;

import com.danding.core.client.PageDTO;
import com.danding.core.domain.utils.ConverterUtil;
import com.danding.mall.app.activity.api.IGbSkusService;
import com.danding.mall.app.activity.vo.request.GbItemPageReq;
import com.danding.mall.app.activity.vo.request.GbItemSkusReq;
import com.danding.mall.app.activity.vo.response.GbItemPageResp;
import com.danding.mall.app.base.session.helper.UserHelper;
import com.danding.mall.app.base.utils.ImageUrlProcessor;
import com.danding.mall.app.domain.activity.gateway.IGbActivityConfigSkuGateway;
import com.danding.mall.app.domain.items.gateway.IItemsGateway;
import com.danding.mall.app.domain.sku.gateway.ISkusGateway;
import com.danding.mall.app.items.dto.ItemsQry;
import com.danding.mall.app.items.dto.data.ItemsDTO;
import com.danding.mall.app.items.dto.item.ItemSpecWithSkuDto;
import com.danding.mall.app.items.vo.response.other.ItemVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
* <p>
    * 商品检索服务类
    * </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Slf4j
@Service
public class GbSkusServiceImpl implements IGbSkusService {

    @Resource
    private IItemsGateway itemsGateway;

    @Resource
    private IGbActivityConfigSkuGateway gbActivityConfigSkuGateway;

    @Resource
    private ISkusGateway skusGateway;

    @Resource
    private ImageUrlProcessor imageUrlProcessor;

    @Override
    public PageDTO<GbItemPageResp> itemsPage(GbItemPageReq gbItemPageReq) {
        ItemsQry itemsQry = new ItemsQry();
        itemsQry.setNameLike(gbItemPageReq.getItemName());
        itemsQry.setBrandName(gbItemPageReq.getBrandName());
        itemsQry.setShopId(UserHelper.getShopId().intValue());
        itemsQry.setId(gbItemPageReq.getItemId());
        itemsQry.setBrandName(gbItemPageReq.getBrandName());
        itemsQry.setBrandId(gbItemPageReq.getBrandId());
        if(gbItemPageReq.getItemStatus() != null){
            itemsQry.setStatus(gbItemPageReq.getItemStatus());
        }
        // 限定为普通商品 非组合品
        itemsQry.setType(1);
        PageDTO<ItemsDTO> page = itemsGateway.page(itemsQry, gbItemPageReq);
        PageDTO<GbItemPageResp> gbItemPageResps = new PageDTO<>();
        List<GbItemPageResp> gPage = new ArrayList<>();
        if(page.getDataList().isEmpty()){
            gbItemPageResps.setPage(page.getPage());
            gbItemPageResps.setDataList(gPage);
            return gbItemPageResps;
        }
        List<Long> itemIds = page.getDataList().stream().map(ItemsDTO::getId).collect(Collectors.toList());
        // 1. 检索该部分item_id是否绑定过活动,若绑定过活动则进行禁用
        Set<Long> longs = gbActivityConfigSkuGateway.selectItemIdsByItemsId(itemIds, UserHelper.getShopId());
        gbItemPageResps.setPage(page.getPage());
        page.getDataList().forEach(itemsDTO -> {
            GbItemPageResp gbItemPageResp = new GbItemPageResp();
            ItemVO itemVO = ConverterUtil.convert(itemsDTO, ItemVO.class);
            itemVO.setMainImage(imageUrlProcessor.restore(itemsDTO.getMainImage()));

            Integer sumStockQuantity = skusGateway.sumStockQuantity(itemsDTO.getId());
            itemVO.setStockQuantity(sumStockQuantity);

            gbItemPageResp.setItemDTO(itemVO);
            if(longs.contains(itemsDTO.getId())){
                gbItemPageResp.setSelected(true);
                gbItemPageResp.setUnSelectReason("该商品已参与其他营销活动");
            }
            gPage.add(gbItemPageResp);
        });
        gbItemPageResps.setDataList(gPage);
        return gbItemPageResps;
    }

    @Override
    public List<ItemSpecWithSkuDto> skusByItems(GbItemSkusReq gbItemSkusReq) {
        List<Long> items = gbItemSkusReq.getItemIds();
        return itemsGateway.findItemSpecWithSkuDtoByItemIds(items);
    }

}