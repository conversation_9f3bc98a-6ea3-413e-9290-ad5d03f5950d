package com.danding.mall.app.coupon;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.danding.core.redis.lock.RedissonDistributedLock;
import com.danding.mall.app.base.session.helper.UserHelper;
import com.danding.mall.app.coupon.api.*;
import com.danding.mall.app.coupon.dto.UserCouponQry;
import com.danding.mall.app.coupon.dto.bo.CouponActivityBO;
import com.danding.mall.app.coupon.dto.data.*;
import com.danding.mall.app.coupon.dto.param.UserCouponGetParam;
import com.danding.mall.app.coupon.dto.vo.CouponDetailVO;
import com.danding.mall.app.coupon.enums.*;
import com.danding.mall.app.domain.coupon.gateway.IUserCouponGateway;
import com.danding.mall.app.domain.user.gateway.IFamilyUserGateway;
import com.danding.mall.app.order.api.IShopOrdersService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
* <p>
    * 用户优惠券表 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2025-02-27
*/
@Slf4j
@Service
public class UserCouponServiceImpl implements IUserCouponService {

    @Resource
    private IUserCouponGateway userCouponGateway;

    @Resource
    private IOrderCouponService orderCouponService;

    @Resource
    private ICouponActivityService activityService;

    @Resource
    private ICouponScopeService scopeService;

    @Resource
    private IShopOrdersService shopOrdersService;

    @Resource
    private IFamilyUserGateway userGateway;

    @Resource
    private IFissionSharerService sharerService;

    @Resource
    private IFissionChildrenService childrenService;

    @Resource
    private RedissonDistributedLock lock;

    @Resource
    private UserCouponServiceImpl self;

    @Resource
    private ICouponCalculateService couponCalculateService;

    @Override
    public Boolean receiveCoupon(UserCouponGetParam param) {
        return lock.lock("MALL_APP:ReceiveCoupon:" + param.getCouponActivityId(), () -> self.doReceiveCoupon(param));
    }

    @Override
    public Boolean receiveMarkingCoupon(Long userId, Long activityId) {
        log.info("领取营销活动券 userId={} activityId={}", userId, activityId);
        CouponActivityBO activityBO = activityService.getById(activityId);
        if (activityBO.getType() != CouponActivityType.MARKETING_ACTIVITY) {
            throw new RuntimeException("非营销活动券");
        }
        for (CouponScopeDTO couponScopeDTO : activityBO.getScopeList()) {
            UserCouponGetParam param = new UserCouponGetParam();
            param.setShopId(activityBO.getShopId());
            param.setUserId(userId);
            param.setCouponActivityId(activityId);
            param.setCouponScopeId(couponScopeDTO.getId());
            param.setSystemSend(true);
            this.receiveCoupon(param);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean doReceiveCoupon(UserCouponGetParam param) {
        log.info("领取优惠券 {}", JSON.toJSONString(param));

        // 参数校验
        if (!canGetCoupon(param)){
            return false;
        }

        // 领取优惠券
        UserCouponDTO temp = new UserCouponDTO();
        temp.setActivityId(param.getCouponActivityId());
        temp.setScopeId(param.getCouponScopeId());
        temp.setShopId(param.getShopId());
        temp.setUserId(param.getUserId());
        CouponActivityDTO activityDTO = activityService.getById(param.getCouponActivityId());
        CouponScopeDTO scopeDTO = scopeService.getById(param.getCouponScopeId());
        temp.setType(scopeDTO.getType().name());
        temp.setScopeValue(scopeDTO.getScopeValue());
        long from = System.currentTimeMillis();
        switch (activityDTO.getUsageTimeType()){
            case LIME: // 限制有效天数
                temp.setUseStartTime(new DateTime(from).toJdkDate());
                temp.setUseEndTime(new DateTime(from + (activityDTO.getValidDays() * 24 * 60 * 60 * 1000L)).toJdkDate());
                break;
            case SAME: // 与领取时间相同
                temp.setUseStartTime(activityDTO.getStartTime());
                temp.setUseEndTime(activityDTO.getEndTime());
                break;
            case CUSTOM: // 自定义
                temp.setUseStartTime(activityDTO.getCustomUseStartTime());
                temp.setUseEndTime(activityDTO.getCustomUseEndTime());
                break;
            default:
                throw new RuntimeException("未指定有效期类型");
        }
        temp.setStatus("1");
        log.info("新增优惠券 {}", JSON.toJSONString(temp));
        userCouponGateway.insert(temp);

        // 已发放数量增加
        // desc: 裂变券是在创建分享的时候就占用了
        if (activityDTO.getType() != CouponActivityType.ASSISTANCE) {
            scopeService.addUseCount(param.getCouponScopeId());
        }

        return true;
    }

    @Override
    public List<UserCouponDTO> listWaitGetCoupons(Long shopId, Long userId, CouponScopeType scopeType, List<String> goodsCodeList) {
        List<CouponActivityBO> activityDTOList = activityService.listByShopId(shopId, CouponActivityStatus.IN_EFFECT);
        List<UserCouponDTO> waitGetList = new ArrayList<>();
        for (CouponActivityBO activityBO : activityDTOList) {
            for (CouponScopeDTO couponScopeDTO : activityBO.getScopeList()) {
                if (scopeType != null){
                    if (!couponScopeDTO.getType().equals(scopeType)){
                        continue; // 筛选类型不匹配，过滤
                    }
                }
                if (!CollectionUtils.isEmpty(goodsCodeList)){ // 空数组表示商品页不限制
                    if (CouponScopeType.PRODUCT.equals(couponScopeDTO.getType())
                            && !goodsCodeList.contains(couponScopeDTO.getScopeValue())){
                        continue; // 商品范围不匹配，过滤
                    }
                }
                UserCouponGetParam param = new UserCouponGetParam();
                param.setCouponScopeId(couponScopeDTO.getId());
                param.setUserId(userId);
                param.setShopId(activityBO.getShopId());
                param.setCouponActivityId(activityBO.getId());
                if (canGetCoupon(param)){
                    UserCouponDTO temp = new UserCouponDTO();
                    temp.setId(-1L); // 设置为-1L，避免区分不出来最优方案是未领取还是未匹配
                    temp.setActivityId(param.getCouponActivityId());
                    temp.setScopeId(param.getCouponScopeId());
                    temp.setShopId(param.getShopId());
                    temp.setUserId(param.getUserId());
                    temp.setStatus(UserCouponStatus.WAIT_USE.getValue());
                    temp.setUseStartTime(new Date());
                    if (activityBO.getUsageTimeType().equals(CouponActivityUsageTimeType.CUSTOM) &&  new DateTime(activityBO.getCustomUseStartTime()).getTime() > System.currentTimeMillis()){
                        continue; // 过滤未到使用时间的未领券
                    }
                    temp.setUseEndTime(new DateTime(System.currentTimeMillis() + 30 * 60 * 1000L).toJdkDate());
                    temp.setType(couponScopeDTO.getType().name());
                    temp.setScopeValue(couponScopeDTO.getScopeValue());
                    waitGetList.add(temp);
                }
            }
        }
        return waitGetList;
    }

    @Override
    public List<UserCouponDTO> listAlreadyGetCoupons(Long shopId, Long userId, CouponScopeType scopeType, List<String> goodsCodeList, String status) {
        List<UserCouponDTO> dataList = userCouponGateway.listByShop(shopId, userId, scopeType, status);
        List<UserCouponDTO> result;
        if (CollectionUtils.isEmpty(goodsCodeList)){
            result = dataList;
        }else {
            result = new ArrayList<>();
            for (UserCouponDTO userCouponDTO : dataList) {
                if (CouponScopeType.PRODUCT.name().equals(userCouponDTO.getType()) && !goodsCodeList.contains(userCouponDTO.getScopeValue())){
                    continue;
                }
                result.add(userCouponDTO);
            }
        }
        return result;
    }

    @Override
    public UserCouponsLib initUserCouponLib(CouponOrder couponOrder) {
        List<UserCouponDTO> totalCouponList = this.listAlreadyGetCoupons(couponOrder.getShopId(), couponOrder.getUserId(), null, null, UserCouponStatus.WAIT_USE.getValue());
        UserCouponsLib couponsLib = new UserCouponsLib();
        Map<String, CouponOrderGoods> goodsMap = new HashMap<>();
        for (CouponOrderGoods goods : couponOrder.getGoodsList()) {
            goodsMap.put(goods.getGoodsCode(), goods);
        }
        List<UserCouponDTO> goodsCouponList = new ArrayList<>();    // 商品券
        List<UserCouponDTO> shopCouponList = new ArrayList<>();     // 店铺券
        List<UserCouponDTO> unAvailableCouponList = new ArrayList<>(); // 不可用券
        for (UserCouponDTO couponDTO : totalCouponList) {
            if (couponDTO.getType().equals(CouponScopeType.SHOP.name())) {
                CouponAvailableResult availableCheckResult = couponCalculateService.availableCheck(couponOrder.getTradeType(), couponOrder.getTotalAmount(), couponOrder.getTotalCount(), couponDTO);
                if (availableCheckResult.available()) {
                    shopCouponList.add(couponDTO);
                } else {
                    unAvailableCouponList.add(this.wrapperReason(couponDTO, availableCheckResult.getErrorMsg()));
                }
            } else if (couponDTO.getType().equals(CouponScopeType.PRODUCT.name())) {
                CouponOrderGoods goods = goodsMap.get(couponDTO.getScopeValue());
                if (goods != null) {
                    CouponAvailableResult availableCheckResult = couponCalculateService.availableCheck(couponOrder.getTradeType(), goods.getGoodsAmount(), goods.getCount(), couponDTO);
                    if (availableCheckResult.available()) {
                        goodsCouponList.add(couponDTO);
                    } else {
                        unAvailableCouponList.add(this.wrapperReason(couponDTO, availableCheckResult.getErrorMsg()));
                    }
                } else {
                    unAvailableCouponList.add(this.wrapperReason(couponDTO, "所结算商品中没有可使用的品"));
                }
            } else {
                unAvailableCouponList.add(couponDTO);
            }
        }
        couponsLib.setGoodsCouponList(goodsCouponList);
        couponsLib.setShopCouponList(shopCouponList);
        couponsLib.setUnAvailableCouponList(unAvailableCouponList);
        return couponsLib;
    }

    private UserCouponDTO wrapperReason(UserCouponDTO dto, String reason) {
        dto.setReason(reason);
        return dto;
    }

    @Override
    public CouponPlan errorPlanFix(CouponSimplePlan simplePlan, Long lastSelectedId) {
      return couponCalculateService.errorPlanFix(simplePlan.getCouponOrder(),simplePlan.getCouponIdList(),lastSelectedId);
    }

    @Override
    public CouponOrderDiscountInfo shareDiscountByOrder(CouponSimplePlan plan) {
        return couponCalculateService.shareDiscountByOrder(plan.getCouponOrder(), plan.getCouponIdList());
    }

    /**
     * 返回最佳折扣方案，包括金额和明细
     * @param param
     * @return
     */
    @Override
    public CouponPlanRichInfo optimizationCoupons(CouponOrder param, Boolean containUnGetCoupon) {
        return couponCalculateService.optimizationCoupons(param,containUnGetCoupon);
    }

    @Override
    public List<OrderCouponDTO> useByOrder(CouponSimplePlan simplePlan) {
        for (Long id : simplePlan.getCouponIdList()) {
            UserCouponDTO couponDTO = this.getById(id);
            if (System.currentTimeMillis() > new DateTime(couponDTO.getUseEndTime()).getTime()){
                throw new RuntimeException("部分优惠已失效，请重新确认");
            }
        }
        return lock.lock("MALL_APP:doUseByOrder:" + UserHelper.getUserId(), () -> self.doUseByOrder(simplePlan.getCouponOrder(), simplePlan.getCouponIdList()));
    }

    @Transactional
    public List<OrderCouponDTO> doUseByOrder(CouponOrder order, List<Long> couponIdList) {
        log.info("使用优惠券 {}{}", JSON.toJSONString(order), JSON.toJSONString(couponIdList));
        // Step::变更优惠券使用状态
        List<UserCouponDTO> updateTemplate = new ArrayList<>();
        List<OrderCouponDTO> orderCouponDTOList = new ArrayList<>();
        // 均摊券金额，并记录
        CouponOrderDiscountInfo discountInfo = couponCalculateService.shareDiscountByOrder(order, couponIdList);
        for (CouponDiscountGoods discountGoods : discountInfo.getGoodsList()) {
            for (CouponDiscountGoods.GoodsDiscountDetail detail : discountGoods.getDiscountDetailList()) {
                UserCouponDTO temp = new UserCouponDTO();
                temp.setId(detail.getCouponDTO().getId());
                temp.setStatus(UserCouponStatus.USED.getValue());
                updateTemplate.add(temp);
                for (CouponPlanGoods.LinkOrder linkOrder : discountGoods.getLinkOrderList()) {
                    // Step::记录优惠券订单使用明细
                    OrderCouponDTO orderCouponDTO = new OrderCouponDTO();
                    orderCouponDTO.setOrderId(linkOrder.getOrderId());
                    orderCouponDTO.setOrderDetailId(linkOrder.getLineNo());
                    orderCouponDTO.setCouponId(detail.getCouponDTO().getId());
                    orderCouponDTO.setStatus("1");
                    UserCouponDTO userCouponDTO = detail.getCouponDTO();
                    orderCouponDTO.setScopeId(userCouponDTO.getScopeId());
                    orderCouponDTO.setActivityId(userCouponDTO.getActivityId());
                    BigDecimal rate = new BigDecimal(linkOrder.getCount()).divide(new BigDecimal(discountGoods.getCount()), 2, RoundingMode.HALF_UP);
                    BigDecimal discount = detail.getDiscount().multiply(rate).setScale(2, RoundingMode.HALF_UP);
                    orderCouponDTO.setDiscount(discount.longValue());
                    orderCouponDTO.setShopId(userCouponDTO.getShopId());
                    orderCouponDTO.setUserId(userCouponDTO.getUserId());
                    orderCouponDTOList.add(orderCouponDTO);
                    orderCouponService.create(orderCouponDTO);
                }
            }
        }
        userCouponGateway.updateBatch(updateTemplate);
        return orderCouponDTOList;
    }

    @Override
    public void cancelUseByOrder(Long orderId) {
        log.info("取消使用优惠券 {}", orderId);
        List<OrderCouponDTO> orderCouponDTOList = orderCouponService.listByOrderId(orderId, "1");
        orderCouponService.batchCancelOrder(orderCouponDTOList.stream().map(OrderCouponDTO::getId).collect(Collectors.toList()));
        Set<Long> idSet = orderCouponDTOList.stream().map(OrderCouponDTO::getCouponId).collect(Collectors.toSet());
        for (Long id : idSet) {
            if (CollectionUtils.isEmpty(orderCouponService.listByCouponId(id, "1"))){ // 无关联订单再进行券回退
                UserCouponDTO userCouponDTO = userCouponGateway.selectById(id);
                if (new DateTime(userCouponDTO.getUseEndTime()).getTime() > System.currentTimeMillis()){
                    UserCouponDTO temp = new UserCouponDTO();
                    temp.setId(id);
                    temp.setStatus(UserCouponStatus.WAIT_USE.getValue());
                    userCouponGateway.update(temp);
                }
            }
        }
    }

    @Override
    public UserCouponDTO getById(Long id) {
        return userCouponGateway.selectById(id);
    }

    @Override
    public String unGetReasonQuery(UserCouponGetParam param) {
        // 判断是否具有领取资格（根据不同券的类型）
        CouponActivityDTO activityDTO = activityService.getById(param.getCouponActivityId());
        CouponScopeDTO scopeDTO = scopeService.getById(param.getCouponScopeId());
        // 活动有效期
        if (System.currentTimeMillis() > new DateTime(activityDTO.getEndTime()).getTime()){
            return "活动已结束";
        }
        if (!activityDTO.getStatus().equals(CouponActivityStatus.IN_EFFECT)){
            return "活动状态不为进行中";
        }
        Long lastShopOrderTime = shopOrdersService.getLatestOrderTime(param.getShopId(),param.getUserId(),null, 0);
        Long lastActiveShopOrderTime = shopOrdersService.getLatestOrderTime(param.getShopId(),param.getUserId(),null, 1);
        Long lastItemOrderTime = null;
        Long lastActiveItemOrderTime = null;
        if (scopeDTO.getType().equals(CouponScopeType.PRODUCT)){
            lastItemOrderTime = shopOrdersService.getLatestOrderTime(param.getShopId(),param.getUserId(),Long.valueOf(scopeDTO.getScopeValue()), 0);
            lastActiveItemOrderTime = shopOrdersService.getLatestOrderTime(param.getShopId(),param.getUserId(),Long.valueOf(scopeDTO.getScopeValue()), 1);
        }
        // 人群类型校验
        switch (activityDTO.getUserScope()){
            case ALL_USER: // 所有用户
                break;
            case SHOP_PURCHASER: //店铺已购用户
                // 近365天在本店铺下过单
                if (lastShopOrderTime == null || lastShopOrderTime < DateUtil.beginOfDay(new DateTime()).getTime() - (365L * 24 * 60 * 60 * 1000)){
                    return "店铺没下过单，或者单子早于365天";
                }
                break;
            case PRODUCT_PURCHASER: // 商品已购用户
                if (lastItemOrderTime == null || lastItemOrderTime < DateUtil.beginOfDay(new DateTime()).getTime() - (180L * 24 * 60 * 60 * 1000)){
                    return "商品没下过单，或者单子早于180天";
                }
                break;
            case RECLAIMED_USER: // 流失挽回用户
                if (lastShopOrderTime == null || lastShopOrderTime < DateUtil.beginOfDay(new DateTime()).getTime() - (365L * 24 * 60 * 60 * 1000)){
                    return "店铺没下过单，或者单子早于365天";
                }
                if (lastShopOrderTime > System.currentTimeMillis() - (30L * 24 * 60 * 60 * 1000)){
                    return "存在单子晚于近30天，不为流失客户";
                }
                Long lastLoginTime = userGateway.selectLastLoginTime(param.getShopId(),param.getUserId());
                if (lastLoginTime < DateUtil.endOfDay(new DateTime()).getTime() - (30L * 24 * 60 * 60 * 1000)){
                    return "30天内无登录记录，不为流失客户";
                }
                break;
            case SHOP_NEW_USER: // 店铺新客
                if (lastActiveShopOrderTime != null && lastActiveShopOrderTime != 0){
                    return "存在有效订单，不为新客";
                }
                break;
            case PRODUCT_NEW_USER: // 商品新客
                if (lastActiveItemOrderTime != null && lastActiveItemOrderTime != 0){
                    return "存在有效订单，不为新客";
                }
                break;
        }

        // 发放数量
        if (scopeDTO.getQuantity() - scopeDTO.getReceiveQuantity() <= 0 && activityDTO.getType() != CouponActivityType.ASSISTANCE) {
            return "可发放数量不足";
        }
        // 券特征限制要求
        if (CouponActivityType.ASSISTANCE.equals(activityDTO.getType())){
            // 检查分享者资质
            if ("sharer".equals(scopeDTO.getScopeValue())){
                FissionSharerDTO sharerDTO = sharerService.getByCouponActivity(param.getUserId(),param.getCouponActivityId(),param.getCouponScopeId());
                if (sharerDTO == null) {
                    return "未发起助力活动";
                }
                List<FissionChildrenDTO> childrenList = childrenService.listBySharerId(sharerDTO.getId());
                if (childrenList.size() < activityDTO.getFissionNum()){
                    return "助力人数不足";
                }
            }
            // 检查助力者资质
            if ("shared".equals(scopeDTO.getScopeValue())){
                FissionChildrenDTO childrenDTO = childrenService.getByScopeAndUserId(param.getCouponActivityId(),param.getCouponScopeId(), param.getUserId());
                if (childrenDTO == null){
                    return "未助力，不满足领取条件";
                }
            }
        }
        if (CouponActivityType.MARKETING_ACTIVITY.equals(activityDTO.getType())) {
            if (!param.getSystemSend()) {
                return "非系统发送，营销活动券不可自动领取";
            }
        }
        // 营销券没有这两个限制
        if (!CouponActivityType.MARKETING_ACTIVITY.equals(activityDTO.getType())) {
            // 判断是否已经重复领取（可能需要加并发锁）
            UserCouponQry userCouponQry = new UserCouponQry();
            userCouponQry.setUserId(param.getUserId());
            userCouponQry.setScopeId(param.getCouponScopeId());
            userCouponQry.setStatus("1");
            List<UserCouponDTO> userCoupon = userCouponGateway.selectList(userCouponQry);
            if (CollUtil.isNotEmpty(userCoupon)) {
                return "已经领过了";
            }
            // 判断单人限领数量
            userCouponQry = new UserCouponQry();
            userCouponQry.setUserId(param.getUserId());
            userCouponQry.setScopeId(param.getCouponScopeId());
            Long count = userCouponGateway.count(userCouponQry);
            if (count >= scopeDTO.getLimitNum()) {
                return "达到单人领取上限";
            }
        }
        return null;
    }

    /**
     * 判断用户是否有领券资格
     * @param param
     * @return
     */
    private Boolean canGetCoupon(UserCouponGetParam param) {
        String result = this.unGetReasonQuery(param);
        if(StringUtils.isEmpty(result)){
            log.info("判断用户是否有领券资格 具备资格");
            return true;
        }
        log.info("判断用户是否有领券资格 不具备资格 {}", result);
        return false;
    }

    public CouponCountDTO countCoupons(Long shopId, Long userId){
        List<UserCouponDTO> userCouponDTOList = this.listAlreadyGetCoupons(shopId
                ,userId
                , null // 券类型不指定
                , null // 生效商品范围不指定
                , UserCouponStatus.WAIT_USE.getValue());
        CouponCountDTO couponCountVO = new CouponCountDTO();
        couponCountVO.setCount(userCouponDTOList.size());
        List<UserCouponDTO> sortedList = userCouponDTOList.stream()
                .sorted(Comparator.comparing(UserCouponDTO::getUseEndTime))
                .collect(Collectors.toList());
        couponCountVO.setDetailList(new ArrayList<>());
        for (UserCouponDTO userCouponDTO : sortedList) {
            if (couponCountVO.getDetailList().size() > 2){
                break;
            }
            CouponDetailVO vo = new CouponDetailVO();
            vo.setId(userCouponDTO.getId());
            vo.setCouponActivityId(userCouponDTO.getActivityId());
            vo.setCouponScopeId(userCouponDTO.getScopeId());
            CouponActivityDTO activityDTO = activityService.getById(userCouponDTO.getActivityId());
            vo.setActivityType(activityDTO.getType());
            CouponScopeDTO scopeDTO = scopeService.getById(userCouponDTO.getScopeId());
            vo.setScopeType(CouponScopeType.valueOf(userCouponDTO.getType()));
            vo.setMethod(scopeDTO.getMethod());
            vo.setDiscount(scopeDTO.getDiscount());
            vo.setDeductionAmount(scopeDTO.getDeductionAmount());
            vo.setThresholdAmount(scopeDTO.getThresholdAmount());
            vo.setThresholdDeductionAmount(scopeDTO.getThresholdDeductionAmount());
            vo.setAlreadyGet(true);
            vo.setScopeValue(scopeDTO.getScopeValue());
            vo.setUsageTimeDesc("还剩" + DateUtil.betweenDay(new Date(), userCouponDTO.getUseEndTime(), false) + "天到期");
            couponCountVO.getDetailList().add(vo);
            // todo:这里的实体类可以重新独立搞一个
        }
        return couponCountVO;
    }

    @Override
    public Long countUsed(Long scopeId) {
        return userCouponGateway.countUsed(scopeId);
    }

    @Override
    public List<UserCouponDTO> getAvailableCoupons(Long userId, Long shopId) {
        return userCouponGateway.selectList(new UserCouponQry().setUserId(userId).setShopId(shopId).setStatus(UserCouponStatus.WAIT_USE.getValue()));
    }

}