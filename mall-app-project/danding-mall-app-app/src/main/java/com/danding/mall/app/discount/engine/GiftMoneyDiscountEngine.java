package com.danding.mall.app.discount.engine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.danding.mall.app.discount.enums.DiscountTypeEnum;
import com.danding.mall.app.discount.model.DiscountContext;
import com.danding.mall.app.discount.model.request.DiscountRequest;
import com.danding.mall.app.discount.model.response.DiscountRecord;
import com.danding.mall.app.discount.model.response.DiscountSkuResponse;
import com.danding.mall.app.discount.model.response.SkuDiscountDetail;
import com.danding.mall.app.domain.items.gateway.IIntermediateInfoGateway;
import com.danding.mall.app.userAmount.api.IOrderDeductionAmountService;
import com.danding.mall.app.userAmount.api.IUserAmountService;
import com.danding.mall.app.userAmount.dto.data.OrderInfoDTO;
import com.danding.mall.app.userAmount.enums.UserAmountTypeEnum;
import com.danding.mall.app.userAmount.vo.OrderInfoVO;
import com.danding.mall.app.userAmount.vo.UserAmountVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 礼金优惠引擎
 * <AUTHOR>
 */
@Slf4j
@Service
public class GiftMoneyDiscountEngine implements DiscountEngine  {

    @Resource
    private IOrderDeductionAmountService orderDeductionAmountService;

    @Resource
    private IUserAmountService userAmountService;

    @Resource
    private IIntermediateInfoGateway intermediateInfoGateway;

    @Override
    public void calculate(DiscountContext context) {
        Long userId = context.getUserId();
        Long shopId = context.getShopId();
        UserAmountVO userAmountVO = userAmountService.detailAmount(userId, shopId);
        log.info("当前用户可用的福豆 福卡数量 userId {} shopId {} userAmount {} ", userId, shopId, JSONUtil.toJsonStr(userAmountVO));
        if (userAmountVO.getGiftAmount().compareTo(BigDecimal.ZERO) == 0) {
            log.info("当前用户没有可使用的福豆 userId {} shopId {}", userId, shopId);
            return;
        }
        // 计算福豆优惠
        List<DiscountSkuResponse> currentSkuList = context.getCurrentSkuList();
        log.info("触发计算福豆优惠逻辑 请求参数 {}", JSONUtil.toJsonStr(currentSkuList));
        Integer tradeType = context.getTradeType();

        List<OrderInfoDTO> orderInfoDTOList = new ArrayList<>();
        for (DiscountSkuResponse currentSku : currentSkuList) {
            if (!currentSku.getWhetherUsedGift()) {
                continue;
            }
            OrderInfoDTO entity = new OrderInfoDTO();
            Long skuId = currentSku.getSkuId();
            entity.setSkuId(skuId);
            entity.setSkuNum(BigDecimal.valueOf(currentSku.getQuantity()));
            entity.setAmount(currentSku.getCurrentPayPrice().setScale(1, RoundingMode.DOWN));
            BigDecimal maxDiscountAmount = intermediateInfoGateway.getMaxDiscountAmountBySkuId(skuId, currentSku.getOriginalPrice()).multiply(BigDecimal.valueOf(currentSku.getQuantity()));
            log.info("当前sku最大能使用的金额 skuId {} maxDiscountAmount {}", skuId, maxDiscountAmount);
            entity.setMaxDiscountAmount(maxDiscountAmount);
            orderInfoDTOList.add(entity);
        }
        if (CollUtil.isEmpty(orderInfoDTOList)) {
            log.warn("当前传入的sku都不可使用福豆");
            return;
        }
        log.info("发起福豆优惠计算 请求参数 {}", JSONUtil.toJsonStr(orderInfoDTOList));
        List<OrderInfoVO> calculationResponseList = null;
        try {
            calculationResponseList = orderDeductionAmountService.recommendDiscountsForProduct(
                    orderInfoDTOList,
                    shopId,
                    userId,
                    UserAmountTypeEnum.BALANCE.getCode(),
                    tradeType
            );
        } catch (Exception e) {
            log.error("福豆优惠计算错误 错误原因 {}", e.getMessage(), e);
            return;
        }
        log.info("福豆优惠计算结果 {}", JSONUtil.toJsonStr(calculationResponseList));
        processGitMoneyDiscount(context, calculationResponseList);
    }

    private void processGitMoneyDiscount(DiscountContext context, List<OrderInfoVO> calculationResponseList) {
        Boolean containGift = context.getRequest().getContainGift();
        List<DiscountSkuResponse> currentSkuList = context.getCurrentSkuList();
        if (CollUtil.isEmpty(calculationResponseList) || CollUtil.isEmpty(currentSkuList)) {
            return;
        }
        Map<Long, OrderInfoVO> giftMoneyDiscountBySkuIdMap = calculationResponseList.stream().collect(Collectors.toMap(OrderInfoVO::getSkuId, Function.identity()));
        for (DiscountSkuResponse sku : currentSkuList) {
            Long skuId = sku.getSkuId();
            OrderInfoVO giftMoneyDiscountResponse = giftMoneyDiscountBySkuIdMap.get(skuId);
            if (ObjUtil.isEmpty(giftMoneyDiscountResponse)) {
                return;
            }
            // 当前需要支付的价格
            BigDecimal currentPayPrice = sku.getCurrentPayPrice();

            // 当前优惠的总金额
            BigDecimal discountAmount = giftMoneyDiscountResponse.getDiscountAmount();

            // 添加优惠明细
            SkuDiscountDetail detail = new SkuDiscountDetail();
            String discountType = DiscountTypeEnum.GIFT.name();
            String discountDesc = DiscountTypeEnum.getDesc(discountType) + "优惠了：" + discountAmount;
            detail.setDiscountType(discountType);
            detail.setDiscountAmount(discountAmount);
            detail.setDiscountDesc(discountDesc);
            sku.getDiscountDetails().add(detail);
            // 记录优惠记录
            DiscountRecord record = new DiscountRecord();
            record.setSkuId(sku.getSkuId());
            record.setDiscountType(discountType);
            record.setDiscountAmount(discountAmount);
            record.setDescription(discountDesc);
            record.setCreateTime(new Date());
            context.getDiscountRecords().add(record);

            // 实际支付价格 = 之前的实际支付价格 - 优惠总价
            currentPayPrice = currentPayPrice.subtract(discountAmount);
            if (containGift) {
                sku.setCurrentPayPrice(currentPayPrice);
                BigDecimal currentTotalDiscountPrice = sku.getTotalDiscountAmount().add(discountAmount);
                sku.setTotalDiscountAmount(currentTotalDiscountPrice);
                sku.setPrice(currentPayPrice.divide(BigDecimal.valueOf(sku.getQuantity()), 6, RoundingMode.DOWN));
            }
        }
        context.setCurrentSkuList(currentSkuList);
    }

    @Override
    public int getOrder() {
        return 2;
    }

    @Override
    public boolean isEnabled(DiscountRequest request) {
        return Boolean.TRUE.equals(request.getGiftCalculationSwitch());
    }

//    private void applyCashDiscount(DiscountContext context) {
//        // 模拟现金优惠 - 全场95折
//        BigDecimal discountRate = new BigDecimal("0.05");
//
//        for (DiscountSkuResponse sku : context.getCurrentSkus()) {
//            BigDecimal discountPerItem = sku.getFinalPrice().multiply(discountRate);
//            BigDecimal totalDiscount = discountPerItem.multiply(new BigDecimal(sku.getQuantity()));
//
//            // 更新SKU价格和优惠信息
//            sku.setFinalPrice(sku.getFinalPrice().subtract(discountPerItem));
//            sku.setTotalDiscountAmount(sku.getTotalDiscountAmount().add(totalDiscount));
//
//            // 添加优惠明细
//            SkuDiscountDetail detail = new SkuDiscountDetail();
//            detail.setDiscountType(DISCOUNT_TYPE);
//            detail.setDiscountAmount(discountPerItem);
//            detail.setDiscountDesc("现金折扣");
//            sku.getDiscountDetails().add(detail);
//
//            // 记录优惠记录
//            DiscountRecord record = new DiscountRecord();
//            record.setSkuId(sku.getSkuId());
//            record.setDiscountType(DISCOUNT_TYPE);
//            record.setDiscountAmount(totalDiscount);
//            record.setDescription("现金折扣:减免" + discountPerItem + "元/件");
//            record.setCreateTime(new Date());
//            context.getDiscountRecords().add(record);
//        }
//    }
}
