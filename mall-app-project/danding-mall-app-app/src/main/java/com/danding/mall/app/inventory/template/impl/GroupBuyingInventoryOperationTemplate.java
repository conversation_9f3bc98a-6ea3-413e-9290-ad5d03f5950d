package com.danding.mall.app.inventory.template.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.danding.mall.app.activity.dto.data.GbGroupInfoDTO;
import com.danding.mall.app.activity.dto.data.GbGroupMemberDTO;
import com.danding.mall.app.activity.enums.GbGroupMemberRoleEnum;
import com.danding.mall.app.activity.enums.GbGroupMemberStatusEnum;
import com.danding.mall.app.domain.activity.gateway.IGbGroupMemberGateway;
import com.danding.mall.app.domain.order.gateway.IOrderShipmentGateway;
import com.danding.mall.app.domain.refund.gateway.IOrderRefundGateway;
import com.danding.mall.app.inventory.enums.InventoryChangeSource;
import com.danding.mall.app.inventory.enums.InventoryChangeType;
import com.danding.mall.app.inventory.template.AbsInventoryOperationTemplate;
import com.danding.mall.app.inventory.enums.InventoryTypeEnum;
import com.danding.mall.app.order.dto.OrderShipmentQry;
import com.danding.mall.app.order.dto.data.OrderShipmentDTO;
import com.danding.mall.app.order.dto.data.SkuOrderDTO;
import com.danding.mall.app.order.enums.ShipmentStatusEnum;
import com.danding.mall.app.refund.enums.RefundType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 拼团购库存操作模板
 */
@Slf4j
@Component
public class GroupBuyingInventoryOperationTemplate extends AbsInventoryOperationTemplate {

    @Resource
    private IGbGroupMemberGateway gbGroupMemberGateway;

    @Resource
    private IOrderShipmentGateway orderShipmentGateway;

    @Resource
    private IOrderRefundGateway orderRefundGateway;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void orderCreation(Long orderId) {
        log.info("分享购库存操作 订单创建 {}", orderId);
        InventoryChangeType inventoryChangeType = InventoryChangeType.ORDER_CREATION;
        InventoryChangeSource inventoryChangeSource = InventoryChangeSource.CUSTOMER_OPERATION;

        List<SkuOrderDTO> skuOrderList = skuOrderGateway.findByShopOrderId(orderId);
        if(CollectionUtil.isEmpty(skuOrderList)){
            log.error("无sku订单");
            return;
        }
        for (SkuOrderDTO skuOrder : skuOrderList) {
            Long shopId = skuOrder.getShopId();
            // 订单创建：减可用库存 + 加已分配库存
            merchantCommodityInventoryGateway.availableToAllocatedOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void orderPayment(Long orderId) {
        log.info("拼团购库存操作 订单支付 {}", orderId);
        InventoryChangeType inventoryChangeType = InventoryChangeType.ORDER_PAYMENT;
        InventoryChangeSource inventoryChangeSource = InventoryChangeSource.CUSTOMER_OPERATION;

        List<SkuOrderDTO> skuOrderList = skuOrderGateway.findByShopOrderId(orderId);
        if(CollectionUtil.isEmpty(skuOrderList)){
            log.error("无sku订单");
            return;
        }
        for (SkuOrderDTO skuOrder : skuOrderList) {
            Long shopId = skuOrder.getShopId();
            // 订单支付：减已分配库存 + 加锁定库存
            merchantCommodityInventoryGateway.allocatedToLockedOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
        }
//        for (SkuOrderDTO skuOrder : skuOrderList) {
//            Long shopId = skuOrder.getShopId();
////            // 订单创建：减可用库存 + 加已分配库存
////            merchantCommodityInventoryGateway.availableToAllocatedOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
////
////            // 订单支付：减可用库存 + 加锁定库存
////            merchantCommodityInventoryGateway.allocatedToLockedOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
//
//            // 订单创建：减可用库存 + 加锁定库存
//            merchantCommodityInventoryGateway.availableToLockedOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void orderShipping(Long orderId) {
        log.info("拼团购库存操作 订单发货 {}", orderId);
        InventoryChangeType inventoryChangeType = InventoryChangeType.ORDER_SHIPPING;
        InventoryChangeSource inventoryChangeSource = InventoryChangeSource.CUSTOMER_OPERATION;

        List<SkuOrderDTO> skuOrderList = skuOrderGateway.findByShopOrderId(orderId);
        if(CollectionUtil.isEmpty(skuOrderList)){
            log.error("无sku订单");
            return;
        }
        for (SkuOrderDTO skuOrder : skuOrderList) {
            Long shopId = skuOrder.getShopId();
            // 订单发货：减锁定库存 + 加在途库存
            merchantCommodityInventoryGateway.lockedToInTransitOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
        }
    }

    @Override
    public void orderCancel(Long orderId) {
        log.info("拼团购库存操作 订单取消 {}", orderId);
        InventoryChangeType inventoryChangeType = InventoryChangeType.ORDER_CANCEL;
        InventoryChangeSource inventoryChangeSource = InventoryChangeSource.CUSTOMER_OPERATION;

        List<SkuOrderDTO> skuOrderList = skuOrderGateway.findByShopOrderId(orderId);
        if(CollectionUtil.isEmpty(skuOrderList)){
            log.error("无sku订单");
            return;
        }
        for (SkuOrderDTO skuOrder : skuOrderList) {
            Long shopId = skuOrder.getShopId();
            // 订单取消：减已分配库存 + 加在可用库存
            merchantCommodityInventoryGateway.allocatedToAvailableOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void orderRefund(Long orderId, GbGroupMemberDTO gbGroupMemberDTO) {
        log.info("拼团购库存操作 订单退款 {}", orderId);
        InventoryChangeType inventoryChangeType = InventoryChangeType.ORDER_REFUND;
        InventoryChangeSource inventoryChangeSource = InventoryChangeSource.CUSTOMER_OPERATION;

        List<SkuOrderDTO> skuOrderList = skuOrderGateway.findByShopOrderId(orderId);
        if(CollectionUtil.isEmpty(skuOrderList)){
            log.error("无sku订单");
            return;
        }
        SkuOrderDTO skuOrderDTO = skuOrderList.get(0);
        Long shopId = skuOrderDTO.getShopId();

//        GbGroupInfoDTO gbGroupInfoDTO = gbGroupMemberGateway.findGroupByOrderId(shopId, orderId);
//        if(gbGroupInfoDTO == null){
//            log.error("找不到团信息 {} {}", shopId, orderId);
//            return;
//        }
        ShipmentStatusEnum shipmentStatus = orderShipmentGateway.getShipmentStatus(orderId);
        log.error("退款库存操作 当前发货状态 {}", shipmentStatus.getDesc());

        switch (shipmentStatus){
            case SHIPPED:
            case UN_SHIPPED:
                // 当前订单处于支付成功
                for (SkuOrderDTO skuOrder : skuOrderList) {
                    // 退款场景(未发货)：减少锁定库存 + 加可用库存
                    merchantCommodityInventoryGateway.lockedToAvailableOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);

//                    // 退款场景(未发货)：减少锁定库存 + 加分配库存
//                    merchantCommodityInventoryGateway.lockedToAllocatedOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
//                    // 退款场景(未发货)：减少分配库存 + 加可用库存
//                    merchantCommodityInventoryGateway.allocatedToAvailableOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
                }
                break;
            // 2是已发货
            case COMPLETED_SHIPMENTS:
//                RefundType refundType = orderRefundGateway.findRefundType(orderId);
//                if(RefundType.ON_SALE_REFUND.equals(refundType)
//                    || RefundType.AFTER_SALE_REFUND.equals(refundType)){
//                    log.info("仅退款");
//                    for (SkuOrderDTO skuOrder : skuOrderList) {
//                        // 退款场景(已发货)：仅减少在途库存
//                        merchantCommodityInventoryGateway.inTransitToAvailableOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
//                    }
//                }else{
                    log.info("非仅退款");
                    for (SkuOrderDTO skuOrder : skuOrderList) {
                        // 退款场景(已发货)：仅减少在途库存
                        merchantCommodityInventoryGateway.inTransitQtyRefundOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
                    }
//                }
                break;
            default:
                log.error("发货状态非法 不进行库存操作 {}", shipmentStatus.getDesc());
        }

    }

    @Override
    public void orderTimeOut(Long shopOrderId) {
        log.info("拼团购库存操作 订单超时 {}", shopOrderId);
        InventoryChangeType inventoryChangeType = InventoryChangeType.ORDER_TIME_OUT;
        InventoryChangeSource inventoryChangeSource = InventoryChangeSource.CUSTOMER_OPERATION;

        List<SkuOrderDTO> skuOrderList = skuOrderGateway.findByShopOrderId(shopOrderId);
        if(CollectionUtil.isEmpty(skuOrderList)){
            log.error("无sku订单");
            return;
        }
        for (SkuOrderDTO skuOrder : skuOrderList) {
            Long shopId = skuOrder.getShopId();
            // 订单取消：减已分配库存 + 加在可用库存
            merchantCommodityInventoryGateway.allocatedToAvailableOperation(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
        }
    }

    @Override
    public InventoryTypeEnum inventoryType() {
        return InventoryTypeEnum.GROUP_BUYING;
    }

}
