package com.danding.mall.app.items;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.core.apm.common.ApmTrace;
import com.danding.core.client.PageDTO;
import com.danding.mall.app.activity.dto.GbActivityConfigSkuQry;
import com.danding.mall.app.activity.dto.data.GbActivityConfigSkuDTO;
import com.danding.mall.app.base.constants.RocketMQConstant;
import com.danding.mall.app.base.model.EsData;
import com.danding.mall.app.base.session.CommonUser;
import com.danding.mall.app.base.session.helper.ShopHelper;
import com.danding.mall.app.base.session.helper.UserHelper;
import com.danding.mall.app.base.utils.EsUtil;
import com.danding.mall.app.base.utils.ImageUrlProcessor;
import com.danding.mall.app.base.utils.TextUtil;
import com.danding.mall.app.core.parana.ParanaClientActuator;
import com.danding.mall.app.core.parana.request.ParanaItemAttrRequest;
import com.danding.mall.app.core.parana.response.ParanaItemAttrResponse;
import com.danding.mall.app.coupon.api.ICouponScopeService;
import com.danding.mall.app.coupon.api.IUserCouponService;
import com.danding.mall.app.coupon.dto.data.CouponScopeDTO;
import com.danding.mall.app.coupon.dto.data.UserCouponDTO;
import com.danding.mall.app.coupon.enums.CouponScopeMethod;
import com.danding.mall.app.deliveryFee.dto.ItemDeliveryFeeQry;
import com.danding.mall.app.deliveryFee.dto.data.DeliveryFeeTemplateDTO;
import com.danding.mall.app.deliveryFee.dto.data.ItemDeliveryFeeDTO;
import com.danding.mall.app.discount.enums.DiscountTypeEnum;
import com.danding.mall.app.discount.handle.DiscountHandle;
import com.danding.mall.app.discount.model.request.DiscountRequest;
import com.danding.mall.app.discount.model.request.DiscountSkuRequest;
import com.danding.mall.app.discount.model.response.DiscountResponse;
import com.danding.mall.app.discount.model.response.DiscountSkuResponse;
import com.danding.mall.app.discount.model.response.SkuDiscountDetail;
import com.danding.mall.app.domain.activity.gateway.IGbActivityConfigSkuGateway;
import com.danding.mall.app.domain.deliveryFee.gateway.IDeliveryFeeTemplateGateway;
import com.danding.mall.app.domain.deliveryFee.gateway.IItemDeliveryFeeGateway;
import com.danding.mall.app.domain.items.gateway.*;
import com.danding.mall.app.domain.shop.gateway.IShopGateway;
import com.danding.mall.app.domain.sku.gateway.ISkuComboRelationGateway;
import com.danding.mall.app.domain.sku.gateway.ISkuCustomGateway;
import com.danding.mall.app.domain.sku.gateway.ISkusGateway;
import com.danding.mall.app.domain.sku.gateway.SkuHistoryGateway;
import com.danding.mall.app.domain.thirdParty.gateway.IThirdPartySkuStockGateway;
import com.danding.mall.app.items.api.IItemOperateCountService;
import com.danding.mall.app.items.api.IItemsService;
import com.danding.mall.app.items.config.FunctionSwitch;
import com.danding.mall.app.items.dto.*;
import com.danding.mall.app.items.dto.data.*;
import com.danding.mall.app.items.dto.item.ItemModel;
import com.danding.mall.app.items.dto.item.ItemSpecWithSkuDto;
import com.danding.mall.app.items.dto.item.SkuRelatedInfoDto;
import com.danding.mall.app.items.dto.sku.OptionalSkuDto;
import com.danding.mall.app.items.enums.*;
import com.danding.mall.app.items.es.ItemsEsIndex;
import com.danding.mall.app.items.message.ItemSyncMessage;
import com.danding.mall.app.items.model.ImageInfo;
import com.danding.mall.app.items.query.ChooseComboProductQuery;
import com.danding.mall.app.items.utils.Digestors;
import com.danding.mall.app.items.utils.SkuPriceRangeResult;
import com.danding.mall.app.items.utils.SkuPriceRangeUtil;
import com.danding.mall.app.items.vo.*;
import com.danding.mall.app.items.vo.items.*;
import com.danding.mall.app.items.vo.other.SkuCommissionConfigParam;
import com.danding.mall.app.items.vo.other.SkuWithCustomParam;
import com.danding.mall.app.items.vo.request.ChangeComboProductPageRequest;
import com.danding.mall.app.items.vo.request.ItemAddOrUpdateRequest;
import com.danding.mall.app.items.vo.response.*;
import com.danding.mall.app.shop.dto.ShopQry;
import com.danding.mall.app.shop.dto.data.ShopDTO;
import com.danding.mall.app.sku.detail.SkuDetailPageTemplate;
import com.danding.mall.app.sku.detail.SkuDetailPageTemplateFactory;
import com.danding.mall.app.sku.dto.SkuComboRelationQry;
import com.danding.mall.app.sku.dto.SkuCustomQry;
import com.danding.mall.app.sku.dto.SkusQry;
import com.danding.mall.app.sku.dto.SkusSpecificationRelevanceQry;
import com.danding.mall.app.sku.dto.data.SkuComboRelationDTO;
import com.danding.mall.app.sku.dto.data.SkuCustomDTO;
import com.danding.mall.app.sku.dto.data.SkusDTO;
import com.danding.mall.app.sku.dto.data.SkusSpecificationRelevanceDTO;
import com.danding.mall.app.sku.enums.SkuExtraIndex;
import com.danding.mall.app.sku.enums.SkuExtraPriceIndex;
import com.danding.mall.app.sku.enums.SkuStatusEnum;
import com.danding.mall.app.sku.enums.SkuStockTypeEnum;
import com.danding.mall.app.sku.mongo.*;
import com.danding.mall.app.sku.stock.SkuStockHandle;
import com.danding.mall.app.sku.vo.param.SkuComboParam;
import com.danding.mall.app.thirdParty.dto.data.ThirdPartySkuStockDTO;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@Slf4j
@Service
public class ItemsServiceImpl implements IItemsService {
    @Value("${beanConvertRate:0.1}")
    private BigDecimal beanConvertRate;
    @Resource
    private IItemsGateway itemsGateway;
    @Resource
    private IItemDetailsGateway itemDetailsGateway;
    @Resource
    private RestHighLevelClient client;
    @Resource
    private IItemAttributesGateway itemAttributesGateway;
    @Resource
    private IShopGateway shopGateway;
    @Resource
    private ISkusGateway skusGateway;
    @Resource
    private IUserCouponService userCouponService;
    @Resource
    private ICouponScopeService couponScopeService;
    @Resource
    private IIntermediateInfoGateway intermediateInfoGateway;
    @Resource
    private ImageUrlProcessor imageUrlProcessor;
    @Resource
    private IItemCollectGateway itemCollectGateway;
    @Resource
    private IItemOperateCountService itemOperateCountService;
    @Resource
    private FunctionSwitch functionSwitch;
    @Resource
    private IThirdPartySkuStockGateway thirdPartySkuStockGateway;
    @Resource
    private IDeliveryFeeTemplateGateway deliveryFeeTemplateGateway;
    @Resource
    private IRestrictedSalesAreaTemplateGateway restrictedSalesAreaTemplateGateway;
    @Resource
    private IItemBrandGateway itemBrandGateway;
    @Resource
    private IItemsService self;
    @Resource
    private IItemDeliveryFeeGateway itemDeliveryFeeGateway;
    @Resource
    private IShopCategoryItemGateway shopCategoryItemGateway;
    @Resource
    private IItemsSpecificationDetailGateway itemsSpecificationDetailGateway;
    @Resource
    private ISkuCustomGateway skuCustomGateway;
    @Resource
    private ISkusSpecificationRelevanceGateway skusSpecificationRelevanceGateway;
    @Resource
    private IBackCategoriesGateway backCategoriesGateway;
    @Resource
    private IShopCategoriesGateway shopCategoriesGateway;
    @Resource
    private ISkuComboRelationGateway skuComboRelationGateway;
    @Resource
    private SpringRocketMQProducer springRocketMQProducer;
    @Resource
    private SkuStockHandle skuStockHandle;
    @Resource
    private IGbActivityConfigSkuGateway gbActivityConfigSkuGateway;

    @Resource
    @Lazy
    private SkuDetailPageTemplateFactory skuDetailPageTemplateFactory;

    @Resource
    private DiscountHandle discountHandle;

    @Resource
    private ParanaClientActuator paranaClientActuator;

    @Resource
    private SkuHistoryGateway skuHistoryGateway;

    @Override
    public List<ItemsDTO> hotSaleRankList(ItemsHotRankQuery itemsHotRankQuery) {
        Long shopId = itemsHotRankQuery.getShopId();
        Long shopCategoryId = itemsHotRankQuery.getShopCategoryId();
        if (shopId == null) {
            throw new RuntimeException("店铺id不能为空");
        }
        if (shopCategoryId == null) {
            throw new RuntimeException("店铺类目id不能为空");
        }

        SearchRequest searchRequest = new SearchRequest("items");
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery.must(QueryBuilders.termQuery("shopId", shopId)));
        searchSourceBuilder.query(boolQuery.must(QueryBuilders.termQuery("status", "1")));
        searchSourceBuilder.query(boolQuery.must(QueryBuilders.termsQuery("shopCategoryIds", Lists.newArrayList(itemsHotRankQuery.getShopCategoryId()))));
        searchSourceBuilder.query(boolQuery.mustNot(QueryBuilders.termQuery("saleQuantity", "0")));
        searchSourceBuilder.sort("saleQuantity", SortOrder.DESC);
        if (itemsHotRankQuery.getPage() != null) {
            searchSourceBuilder.from((itemsHotRankQuery.getPage() - 1) * itemsHotRankQuery.getSize());
        } else {
            searchSourceBuilder.from(0);
        }
        searchSourceBuilder.size(itemsHotRankQuery.getSize());
        searchRequest.source(searchSourceBuilder);

        try {
            List<ItemsDTO> result = new ArrayList<>();
            log.info("排行榜查询es语句 {}", searchSourceBuilder);
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            // 一开始给出去的类字段错了现在前端没时间改了，先用ItemsDTO
            EsData<ItemsEsIndex> esData = EsUtil.getData(searchResponse, ItemsEsIndex.class);
            for (ItemsEsIndex datum : esData.getData()) {
                ItemsDTO item = BeanUtil.copyProperties(datum, ItemsDTO.class);
                result.add(item);
                item.setLowPrice(datum.getPrice());
                item.setHighPrice(datum.getPrice());
            }
            return result;
        } catch (IOException e) {
            log.error("销售热榜查询失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<ItemsDTO> list(ItemsParam itemsParam) {
        ItemsQry param = new ItemsQry();
        param.setItemIds(itemsParam.getItemIds());
        List<ItemsDTO> itemsDTOS = itemsGateway.selectList(param);
        itemsDTOS.forEach(item -> {
            item.setMainImage(imageUrlProcessor.restore(item.getMainImage()));
        });
        return itemsDTOS;
    }

    @Override
    public ItemsDTO findById(Long itemId) {
        ItemsDTO itemsDTO = itemsGateway.selectById(itemId);
        itemsDTO.setMainImage(imageUrlProcessor.restore(itemsDTO.getMainImage()));
        return itemsDTO;
    }

    @Override
    public ViewedItemDetailInfo findViewDetail(Long itemId) {
        ItemDetailsDTO itemDetailsDTO = itemDetailsGateway.selectByItemId(itemId);
        if (itemDetailsDTO == null) {
            log.error("no item detail found for item (id={})", itemId);
            return null;
        }
        ItemsDTO itemsDTO = itemsGateway.selectById(itemId);
        Item item = BeanUtil.copyProperties(itemsDTO, Item.class);
        ItemDetail itemDetail = BeanUtil.copyProperties(itemDetailsDTO, ItemDetail.class);
        processItemForActivity(item, itemDetail);
        setActivityPrice(item);
        if (item.getMainImage() != null) {
            item.setMainImage(imageUrlProcessor.restore(item.getMainImage()));
        }
        List<ImageInfo> images = itemDetail.getImages();
        if (!CollectionUtils.isEmpty(images)) {
            for (ImageInfo image : images) {
                if (image.getUrl() != null) {
                    image.setUrl(imageUrlProcessor.restore(image.getUrl()));
                }
            }
        }
        ItemAttributesDTO itemAttributesDTO = itemAttributesGateway.selectByItemId(itemId);
        if (itemAttributesDTO == null) {
            log.error("no item attribute found for item (id={})", itemId);
            return null;
        }
        ViewedItemDetailInfo viewedItemDetailInfo = new ViewedItemDetailInfo();
        viewedItemDetailInfo.setItemDetail(itemDetail);
        viewedItemDetailInfo.setItem(item);
        viewedItemDetailInfo.setGroupedOtherAttributes(itemAttributesDTO.getOtherAttrs());
        if (viewedItemDetailInfo.getGroupedOtherAttributes() == null) {
            viewedItemDetailInfo.setGroupedOtherAttributes(new ArrayList<>());
        }
//        // 原价转换
//        BigDecimal itemPrice = new BigDecimal(String.valueOf(item.getLowPrice())).divide(new BigDecimal("100"));
//
//        viewedItemDetailInfo.setPrice(itemPrice);
//        // 最便宜的sku的折后价
//        viewedItemDetailInfo.setDiscountPrice(itemPrice);

        // 设置价格与优惠信息
        addDiscountInfo(itemId, item, viewedItemDetailInfo);

        // 先设置价格信息 再判断登录态
        CommonUser user = UserHelper.getUser();
        if (UserHelper.isNotLoggedIn()) {
            return viewedItemDetailInfo;
        }
        try {
            setSkuGbActivityInfo(viewedItemDetailInfo, item);
        } catch (Exception e) {
            log.error("商品详情设置sku的活动信息失败 ", e);
        }

        // 是否收藏
        setCollectInfo(user.getId(), ShopHelper.getId(), viewedItemDetailInfo);
        log.info(itemId + "========详情返回参数======={}", JSON.toJSONString(viewedItemDetailInfo));
        return viewedItemDetailInfo;
    }


    private void addDiscountInfo(Long itemId, Item item, ViewedItemDetailInfo viewedItemDetailInfo) {
        SkusQry skusQry = new SkusQry();
        skusQry.setItemId(itemId);
        skusQry.setStatus(SkuStatusEnum.ON_SHELF.getCode());
        skusQry.setShopId(item.getShopId());
        List<SkusDTO> skuList = skusGateway.selectList(skusQry);

        SkuPriceRangeResult priceRangeResult = SkuPriceRangeUtil.findPriceRange(skuList);
        SkusDTO minPriceSKu = priceRangeResult.getMinPriceSku();
        if(minPriceSKu == null){
            // TODO 商品上没有开启活动价的标记
            log.error("识别不了价格 {}", itemId);
            throw new RuntimeException("系统错误");
        }
        Integer minPrice = priceRangeResult.getMinPrice();
        Integer maxPrice = priceRangeResult.getMaxPrice();

        viewedItemDetailInfo.setPrice(new BigDecimal(String.valueOf(minPrice)).divide(new BigDecimal("100")));
        viewedItemDetailInfo.setMaxPrice(new BigDecimal(String.valueOf(maxPrice)).divide(new BigDecimal("100")));
        log.info("商品最低价 {} {} skuId:{} 是否是活动价:{}", itemId, minPrice, minPriceSKu.getId(), minPriceSKu.isExtraActivityValid());

        List<DiscountSkuRequest> discountSkuRequestList = new ArrayList<>();
        DiscountSkuRequest discountSkuRequest = new DiscountSkuRequest();
        discountSkuRequest.setItemId(itemId);
        discountSkuRequest.setSkuId(minPriceSKu.getId());
        discountSkuRequest.setQuantity(1);
        discountSkuRequestList.add(discountSkuRequest);

//        Integer originPrice = sku.getPrice();
//        if (sku.isExtraActivityValid()) {
//            String activitySalesPrice = sku.getExtraMap().get(SkuExtraIndex.activitySalesPrice.name());
//            originPrice = Integer.parseInt(activitySalesPrice);
//        }
        BigDecimal price = new BigDecimal(minPrice).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        discountSkuRequest.setOriginalPrice(price);

        DiscountRequest discountRequest = new DiscountRequest();
        discountRequest.setDiscountSkuRequestList(discountSkuRequestList);
        // 福豆=礼金  福卡=现金
        discountRequest.setCashCalculationSwitch(false);
        discountRequest.setCouponIds(new ArrayList<>());
        discountRequest.setTradeType(item.getIsBonded());

        DiscountResponse discountResponse = discountHandle.calculateDiscount(discountRequest);

//        viewedItemDetailInfo.setPrice(price);

        List<DiscountSkuResponse> discountSkuResponseList = discountResponse.getDiscountSkuResponseList();
        if (CollUtil.isNotEmpty(discountSkuResponseList)) {
            DiscountSkuResponse lowestPriceItem = discountSkuResponseList.get(0);

            // 实付金额
            viewedItemDetailInfo.setDiscountPrice(lowestPriceItem.getCurrentPayPrice());

            // 追加优惠卷优惠信息
            addCouponDiscountInfo(lowestPriceItem, viewedItemDetailInfo);

            // 追加福豆优惠信息
            addGiftDiscountInfo(lowestPriceItem, viewedItemDetailInfo);
        }
    }

//    private SkusDTO findMinPriceSKu(boolean isExtraActivityValid, List<SkusDTO> skuList) {
//        int minPrice = 0;
//        SkusDTO sku = null;
//        if(isExtraActivityValid){
//            // 取最低活动价
//            for (SkusDTO skuDTO : skuList) {
//                // 注：入口初商品开启活动价后 所有sku都会开启活动价
//                String activitySalesPrice = skuDTO.getExtraMap().get(SkuExtraIndex.activitySalesPrice.name());
//                if (StringUtils.hasText(activitySalesPrice)) {
//                    int price = Integer.parseInt(activitySalesPrice);
//                    if(minPrice < price) {
//                        minPrice = price;
//                        sku = skuDTO;
//                    }
//                }
//            }
//        }else{
//            // 取最低原价
//            for (SkusDTO skuDTO : skuList) {
//                Integer price = skuDTO.getPrice();
//                if(minPrice < price) {
//                    minPrice = price;
//                    sku = skuDTO;
//                }
//            }
//        }
//        return sku;
//    }

    private void addGiftDiscountInfo(DiscountSkuResponse lowestPriceItem, ViewedItemDetailInfo viewedItemDetailInfo) {
//        List<SkuDiscountDetail> discountDetails = lowestPriceItem.getDiscountDetails(DiscountTypeEnum.GIFT);
        if (CollUtil.isEmpty(lowestPriceItem.getDiscountDetails())) {
            log.info("没有福豆优惠");
            return;
        }
        BigDecimal totalGiftDiscountAmount = lowestPriceItem.getTotalDiscountAmount(DiscountTypeEnum.GIFT);
        BigDecimal cashGiftCount = totalGiftDiscountAmount.divide(beanConvertRate, 0, RoundingMode.DOWN);

        viewedItemDetailInfo.setCashGift(totalGiftDiscountAmount);
        viewedItemDetailInfo.setCashGiftCount(cashGiftCount);
//                viewedItemDetailInfo.setDiscountPrice(viewedItemDetailInfo.getDiscountPrice().subtract(totalGiftDiscountAmount));

        ViewCouponInfo viewCouponInfo = new ViewCouponInfo();
        viewCouponInfo.setAmount(totalGiftDiscountAmount.stripTrailingZeros().toPlainString());
        viewCouponInfo.setCouponType("福豆");
        viewCouponInfo.setCouponScope("可用" + cashGiftCount.toPlainString() + "个");

        viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo);
    }


    private void setSkuGbActivityInfo(ViewedItemDetailInfo viewedItemDetailInfo, Item item) {
        Long shopId = item.getShopId();
        List<SkusDTO> skuList = skusGateway.findSkuListByItemId(item.getId());
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        List<Long> skuIds = skuList.stream().map(SkusDTO::getId).collect(Collectors.toList());

        // 查询 商家+商品+SKU集合 分别参与的活动清单
        List<GbActivityConfigSkuDTO> gbActivityConfigSkuList = gbActivityConfigSkuGateway.getInvalidSkuInActivity(skuIds, shopId);

        if (CollectionUtils.isEmpty(gbActivityConfigSkuList)) {
            viewedItemDetailInfo.setHasGbActivity(false);
        } else {
            viewedItemDetailInfo.setHasGbActivity(true);
        }
    }

//    private void setCashGiftInfo(Long itemId, ViewedItemDetailInfo viewedItemDetailInfo, CommonUser user, int tradeType) {
//        BigDecimal maxDiscountAmount = intermediateInfoGateway.getMaxDiscountAmountBySkuId(itemId);
//        if (maxDiscountAmount != null) {
//            List<OrderInfoDTO> orderInfoDTOList = new ArrayList<>();
//            OrderInfoDTO orderInfoDTO = new OrderInfoDTO();
//            orderInfoDTO.setSkuId(itemId);
//            orderInfoDTO.setAmount(viewedItemDetailInfo.getDiscountPrice());
//            orderInfoDTO.setMaxDiscountAmount(maxDiscountAmount);
//            orderInfoDTO.setSkuNum(new BigDecimal("1"));
//            orderInfoDTOList.add(orderInfoDTO);
//
//            List<OrderInfoVO> orderInfoVOS = orderDeductionAmountService.recommendDiscountsForProduct(orderInfoDTOList, user.getShopId(), user.getId(), 1, tradeType);
//
//            if (!CollectionUtils.isEmpty(orderInfoVOS)) {
//                OrderInfoVO next = orderInfoVOS.iterator().next();
//                if (next.getDiscountAmount() != null) {
//                    BigDecimal discountAmount = next.getDiscountAmount().setScale(2, RoundingMode.DOWN);
//                    viewedItemDetailInfo.setCashGift(discountAmount);
//                    viewedItemDetailInfo.setCashGiftCount(discountAmount.multiply(beanConvertRate));
//                    viewedItemDetailInfo.setDiscountPrice(viewedItemDetailInfo.getDiscountPrice().subtract(discountAmount));
//                    ViewCouponInfo viewCouponInfo = new ViewCouponInfo();
//                    viewCouponInfo.setAmount(discountAmount.stripTrailingZeros().toPlainString());
//                    viewCouponInfo.setCouponType("福豆");
//                    viewCouponInfo.setCouponScope("可抵" + discountAmount.stripTrailingZeros().toPlainString());
//
//                    viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo);
//                }
//            }
//        }
//    }

//    private void setCouponInfo(CommonUser user, int tradeType, Item item, ViewedItemDetailInfo viewedItemDetailInfo) {
//        CouponOrder param = new CouponOrder();
//        param.setUserId(user.getId());
//        param.setShopId(user.getShopId());
//        param.setTradeType(tradeType);
//        List<CouponOrderGoods> goodsList = new ArrayList<>();
//        CouponOrderGoods couponsItem = new CouponOrderGoods();
//        goodsList.add(couponsItem);
//        couponsItem.setLineNo(item.getId());
//        couponsItem.setGoodsCode(item.getId().toString());
//        couponsItem.setCount(1);
//        couponsItem.setSinglePrice(viewedItemDetailInfo.getPrice());
//        param.setGoodsList(goodsList);
//        log.info("CartItemsServiceImpl.setCartItemDiscountPrice couponsOrderTemplate={}", JSON.toJSONString(param));
//        CouponPlanRichInfo coupons = userCouponService.optimizationCoupons(param,true);
//        log.info("CartItemsServiceImpl.setCartItemDiscountPrice coupons={}", JSON.toJSONString(coupons));
//        List<UserCouponDTO> goodsCouponList = coupons.getGoodsCouponList();
//        List<CouponPlanGoods> couponGoodsList = coupons.getGoodsList();
//        if (CollectionUtils.isEmpty(couponGoodsList)) {
//            log.error("单品最优优惠计算失败, {}", JSON.toJSONString(param));
//            return;
//        }
//        CouponPlanGoods planGoods = couponGoodsList.iterator().next();
//
//        // 只有一个商品的时候，商品的优惠金额就是总金额 totalGoodsCoupon totalShopCoupon 不为null，可能为0
//        BigDecimal discountPrice = coupons.getDiscountInfo().getLineTotalDiscount(planGoods.getLineNo()).setScale(2, RoundingMode.DOWN);
//        viewedItemDetailInfo.setCouponsPrice(discountPrice);
//        viewedItemDetailInfo.setDiscountPrice(viewedItemDetailInfo.getPrice().subtract(discountPrice));
//
//        viewedItemDetailInfo.setViewCouponInfos(new ArrayList<>());
//        if (!CollectionUtils.isEmpty(goodsCouponList)) {
//            // 当1个商品只有一个商品优惠券
//            UserCouponDTO userCouponDTO = goodsCouponList.get(0);
//            ViewCouponInfo viewCouponInfo = addCouponScopeInfo(userCouponDTO, coupons.getDiscountInfo().getTotalGoodsCoupon());
//            viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo);
//        }
//        UserCouponDTO shopCoupon = coupons.getShopCoupon();
//        if (shopCoupon != null) {
//            ViewCouponInfo viewCouponInfo = addCouponScopeInfo(shopCoupon, coupons.getDiscountInfo().getTotalShopCoupon());
//            viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo);
//        }
//    }

    private ViewCouponInfo addCouponScopeInfo(DiscountSkuResponse lowestPriceItem, DiscountTypeEnum discountType) {
        List<SkuDiscountDetail> discountDetails = lowestPriceItem.getDiscountDetails(discountType);
        BigDecimal totalDiscountAmount = lowestPriceItem.getTotalDiscountAmount(discountType);

        if (CollUtil.isNotEmpty(discountDetails)) {
            SkuDiscountDetail skuDiscountDetail = discountDetails.get(0);
            // n1：这里为什么会有-1的值
            if (skuDiscountDetail.getUserCouponId() != -1) {
                UserCouponDTO userCouponDTO = userCouponService.getById(skuDiscountDetail.getUserCouponId());
                ViewCouponInfo viewCouponInfo = addCouponScopeInfo(userCouponDTO, totalDiscountAmount);
                return viewCouponInfo;
            }
        }
        return null;
    }

    private void addCouponDiscountInfo(DiscountSkuResponse lowestPriceItem, ViewedItemDetailInfo viewedItemDetailInfo) {
        List<String> discountTypeList = new ArrayList<>();
        discountTypeList.add(DiscountTypeEnum.PRODUCT_COUPON.getCode());
        discountTypeList.add(DiscountTypeEnum.SHOP_COUPON.getCode());

        // 优惠券金额
        BigDecimal couponsPrice = lowestPriceItem.sumDiscountAmount(discountTypeList);
        viewedItemDetailInfo.setCouponsPrice(couponsPrice);

        // 优惠券明细:商品优惠卷
        ViewCouponInfo viewCouponInfo = addCouponScopeInfo(lowestPriceItem, DiscountTypeEnum.PRODUCT_COUPON);
        if (viewCouponInfo != null) {
            viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo);
        }

        // 优惠券明细：店铺优惠卷
        ViewCouponInfo viewCouponInfo2 = addCouponScopeInfo(lowestPriceItem, DiscountTypeEnum.SHOP_COUPON);
        if (viewCouponInfo2 != null) {
            viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo2);
        }

//        viewedItemDetailInfo.setViewCouponInfos(new ArrayList<>());
//        if (!CollectionUtils.isEmpty(goodsCouponList)) {
//            // 当1个商品只有一个商品优惠券
//            UserCouponDTO userCouponDTO = goodsCouponList.get(0);
//            ViewCouponInfo viewCouponInfo = addCouponScopeInfo(userCouponDTO, coupons.getDiscountInfo().getTotalGoodsCoupon());
//            viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo);
//        }
//        UserCouponDTO shopCoupon = coupons.getShopCoupon();
//        if (shopCoupon != null) {
//            ViewCouponInfo viewCouponInfo = addCouponScopeInfo(shopCoupon, coupons.getDiscountInfo().getTotalShopCoupon());
//            viewedItemDetailInfo.getViewCouponInfos().add(viewCouponInfo);
//        }
    }

    private ViewCouponInfo addCouponScopeInfo(UserCouponDTO userCouponDTO, BigDecimal coupons) {
        ViewCouponInfo viewCouponInfo = new ViewCouponInfo();
        CouponScopeDTO scopeDTO = couponScopeService.getById(userCouponDTO.getScopeId());
        viewCouponInfo.setAmount(coupons.setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString());
        viewCouponInfo.setCouponType(scopeDTO.getType().getDesc() + "券");

        CouponScopeMethod method = scopeDTO.getMethod();
        String couponScope = "";
        switch (method) {
            case DIRECT_REDUCTION:
                if (scopeDTO.getDeductionAmount() != null) {
                    couponScope = "立减" + scopeDTO.getDeductionAmount().stripTrailingZeros().toPlainString() + "元";
                }
                break;
            case FULL_REDUCTION:
                if (scopeDTO.getThresholdAmount() != null) {
                    couponScope = "满" + scopeDTO.getThresholdAmount().stripTrailingZeros().toPlainString() + "减" + scopeDTO.getThresholdDeductionAmount();
                }
                break;
            case DISCOUNT:
                if (scopeDTO.getDiscount() != null) {
                    couponScope = scopeDTO.getDiscount().stripTrailingZeros().toPlainString() + "折";
                }
                break;
        }
        viewCouponInfo.setCouponScope(couponScope);
        return viewCouponInfo;
    }

    private void setCollectInfo(Long userId, Long shopId, ViewedItemDetailInfo detailInfo) {
        Item item = detailInfo.getItem();
        item.setCollect(Boolean.FALSE);
        ItemCollectDTO itemCollectDTO = itemCollectGateway.selectUserItemCollect(userId, shopId, item.getId());
        if (Objects.nonNull(itemCollectDTO) && Objects.equals(1, itemCollectDTO.getStatus())) {
            item.setCollect(Boolean.TRUE);
        }
    }

    public void processItemForActivity(Item item, ItemDetail itemDetail) {
        if (item == null || !item.isExtraActivityValid()) {
            return;
        }

        try {
            boolean isUseActivityData = false;
            //主图
            String mainImage = item.getMainImage_();
            String activityMainImage = item.getExtra().get(ItemExtraIndex.activityMainImage.name());
            if (StringUtils.hasText(activityMainImage)) {
                mainImage = activityMainImage;
                isUseActivityData = true;
            }

            //明细图
            List<ImageInfo> images = itemDetail.getImages();
            String activityDetailImages = item.getExtra().get(ItemExtraIndex.activityDetailImages.name());
            if (StringUtils.hasText(activityDetailImages)) {
                List<ImageInfo> list = Arrays.stream(activityDetailImages.split(","))
                        .filter(StringUtils::hasText)
                        .map(e -> new ImageInfo("", e))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(list)) {
                    images = ImageInfo.completeImageUrl(list);
                    isUseActivityData = true;
                }
            }

            item.setMainImage(imageUrlProcessor.restore(mainImage));
            for (ImageInfo image : images) {
                image.setUrl(imageUrlProcessor.restore(image.getUrl()));
            }
            itemDetail.setImages(images);
            item.getExtra().put(ItemExtraIndex.isUseExtraActivity.name(), Boolean.toString(isUseActivityData));
        } catch (Exception ex) {
            log.error("processItemForActivity fail, itemId={}", item.getId(), ex);
        }
    }

    private void setActivityPrice(Item item) {
        List<SkusDTO> skuList = skusGateway.findSkuListByItemId(item.getId());
        if (CollUtil.isEmpty(skuList)) {
            return;
        }
        List<Integer> skuPriceList = skuList.stream().filter(SkusDTO::isExtraActivityValid).map(sku -> sku.getExtraMap().get(SkuExtraIndex.activitySalesPrice.name())).map(Integer::parseInt)
                .sorted(Comparator.comparingInt(Integer::intValue)).collect(Collectors.toList());
        //更改售价
        if (CollUtil.isNotEmpty(skuPriceList)) {
            item.setLowPrice(skuPriceList.get(0));
            item.setHighPrice(skuPriceList.get(0));
        }
    }

    @Override
    public SearchedItemNew miniPageItem(ItemSearchParam itemSearchParam) {
        Long shopIdReq = itemSearchParam.getShopId();
        SearchedItemNew searchedItemNew = new SearchedItemNew();

        CommonUser currentUser = UserHelper.user();
        if (UserHelper.isLoggedIn()) {
            itemSearchParam.setShopId(currentUser.getShopId());
        } else {
            itemSearchParam.setShopId(ShopHelper.getId());
        }

        SearchRequest searchRequest = createQueryParamsBuild(itemSearchParam);
        List<ItemsEsIndex> content = null;
        long currentTimeMillis = System.currentTimeMillis();
        try {
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            log.info("搜索耗时: {}", searchResponse.getTook().getMillis());
            log.info("搜索方法耗时: {}", System.currentTimeMillis() - currentTimeMillis);
            EsData<ItemsEsIndex> data = EsUtil.getData(searchResponse, ItemsEsIndex.class);
            content = data.getData();
            searchedItemNew.setTotal(data.getHitTotal());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (CollectionUtils.isEmpty(content)) {
            return searchedItemNew;
        }
        List<SearchedItemNew.ItemsNew> itemsNews = new ArrayList<>();
        Map<Long, List<SkusDTO>> itemSkuMap = new HashMap<>();
        for (ItemsEsIndex itemsEsIndex : content) {
            SearchedItemNew.ItemsNew itemsNew = new SearchedItemNew.ItemsNew();
            itemsNew.setId(itemsEsIndex.getId());
            itemsNew.setShopId(itemsEsIndex.getShopId());
            itemsNew.setShopName(itemsEsIndex.getShopName());
            itemsNew.setPrice(itemsEsIndex.getPrice());
            itemsNew.setSaleQuantity(itemsEsIndex.getSaleQuantity());
            itemsNew.setIsBonded(itemsEsIndex.getIsBonded());
            if (itemsEsIndex.getMainImage() != null) {
                itemsNew.setMainPic(imageUrlProcessor.restore(itemsEsIndex.getMainImage()));
            }
            itemsNew.setTitle(infoNames(itemsEsIndex.getName()));

            SkusQry skusQry = new SkusQry();
            skusQry.setItemId(itemsNew.getId());
            skusQry.setShopId(shopIdReq);
            skusQry.setStatus(SkuStatusEnum.ON_SHELF.getCode());
            List<SkusDTO> skuList = skusGateway.selectList(skusQry);
            itemsNew.setWhetherExistMultiSkus(skuList.size() > 1);
            if (itemsEsIndex.getOriginId() != null) {
                JSONObject o = new JSONObject();
                o.put("id", itemsEsIndex.getOriginId());
                o.put("icon", itemsEsIndex.getOriginUrl());
                o.put("name", itemsEsIndex.getOrigin());
                itemsNew.setOrigin(o);
            }
            itemsNews.add(itemsNew);
            itemSkuMap.put(itemsNew.getId(), skuList);
        }

        appendAdditionalItemInfo(itemsNews);
        appendAdditionalSkuInfo(itemsNews);

        searchedItemNew.setList(itemsNews);

        log.info("组装后耗时: {}", System.currentTimeMillis() - currentTimeMillis);

        // 计算折后价
        if (Boolean.TRUE.equals(itemSearchParam.getCalculateDiscount()) && UserHelper.isLoggedIn()) {
            Long userId = currentUser.getId();
            Long shopId = currentUser.getShopId();
            List<Long> itemIdList = itemsNews.stream().map(SearchedItemNew.ItemsNew::getId).collect(Collectors.toList());
            List<ItemsDTO> itemsDTOS = itemsGateway.selectIds(itemIdList);
            Map<Long, ItemsDTO> itemsMap = itemsDTOS.stream().collect(Collectors.toMap(ItemsDTO::getId, Function.identity(), (v1, v2) -> v1));
//            // 优惠券福豆折扣金额计算
//            setCouponDiscountPrice(itemsMap, itemsNews, userId, shopId);
//            setCashGiftDiscountPrice(itemsNews, itemsMap, userId, shopId);

            setItemDiscountPrice(itemsMap, itemsNews, userId, shopId, itemSkuMap);
        } else {
            itemsNews.forEach(item -> item.setDiscountPrice(new BigDecimal(String.valueOf(item.getPrice())).divide(new BigDecimal("100"))));
        }
        // 收藏信息
        setCollectInfo(UserHelper.getUserId(), shopIdReq, itemsNews);
        log.info("计算优惠后耗时: {}", System.currentTimeMillis() - currentTimeMillis);
        return searchedItemNew;
    }


    private void setCollectInfo(Long userId, Long shopId, List<SearchedItemNew.ItemsNew> itemsNews) {
        List<Long> itemIdList = itemsNews.stream().map(SearchedItemNew.ItemsNew::getId).collect(Collectors.toList());
        Map<Long, ItemCollectDTO> collectMap = new HashMap<>();
        if (Objects.nonNull(userId)) {
            List<ItemCollectDTO> itemCollectDTOS = itemCollectGateway.selectUserItemCollect(userId, shopId, itemIdList);
            collectMap = itemCollectDTOS.stream().collect(Collectors.toMap(ItemCollectDTO::getItemId, Function.identity(), (v1, v2) -> v1));
        }
        List<ItemOperateCountDTO> itemOperateCountDTOS = itemOperateCountService.selectOperateCount(shopId, ItemOperateCountType.COLLECT.getCode(), itemIdList);
        Map<Long, ItemOperateCountDTO> operateCountMap = itemOperateCountDTOS.stream().collect(Collectors.toMap(ItemOperateCountDTO::getItemId, Function.identity(), (v1, v2) -> v1));
        for (SearchedItemNew.ItemsNew itemsNew : itemsNews) {
            ItemCollectDTO itemCollectDTO = collectMap.get(itemsNew.getId());
            if (Objects.nonNull(itemCollectDTO) && ItemCollectStatus.COLLECT.getCode().equals(itemCollectDTO.getStatus())) {
                itemsNew.setCollect(true);
            }
            ItemOperateCountDTO itemOperateCountDTO = operateCountMap.get(itemsNew.getId());
            if (Objects.nonNull(itemOperateCountDTO)) {
                itemsNew.setCollectNum(itemOperateCountDTO.getCount());
                itemsNew.setCollectNumStr(TextUtil.formatCollectNumber(itemsNew.getCollectNum()));
            }
        }
    }

    private void setItemDiscountPrice(Map<Long, ItemsDTO> itemsMap, List<SearchedItemNew.ItemsNew> itemsNews, Long userId, Long shopId, Map<Long, List<SkusDTO>> itemSkuMap) {
        for (SearchedItemNew.ItemsNew itemsNew : itemsNews) {
            ItemsDTO item = itemsMap.get(itemsNew.getId());
            Long itemId = item.getId();

            List<SkusDTO> skusDTOList = itemSkuMap.get(itemId);
            SkuPriceRangeResult priceRangeResult = SkuPriceRangeUtil.findPriceRange(skusDTOList);

            // 找到最小值
            Integer minPrice = priceRangeResult.getMinPrice();
            // 找到最大值
            Integer maxPrice = priceRangeResult.getMaxPrice();

            log.info("商品列表页价格计算 {} {}", itemId, minPrice);
            // 【待处理】页面没有除以100 库里是分
            itemsNew.setDiscountPrice(new BigDecimal(minPrice).divide(new BigDecimal(100), 2, RoundingMode.DOWN));
            itemsNew.setMaxDiscountPrice(new BigDecimal(maxPrice).divide(new BigDecimal(100), 2, RoundingMode.DOWN));
        }
    }

    //过滤所有的搜索<em></em>
    private String infoNames(String name) {
        return name.replaceAll("<em>", "").replaceAll("</em>", "");
    }

    public void appendAdditionalItemInfo(List<SearchedItemNew.ItemsNew> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }
        //查询商品基本信息（key=item.id）
        Map<Long, ItemsDTO> itemMap = findItemMap(sourceList.stream().map(SearchedItemNew.ItemsNew::getId).collect(Collectors.toList()));
        //TODO 查询收藏信息

        //填充
        sourceList.forEach(view -> {
            ItemsDTO itemsDTO = itemMap.get(view.getId());
            appendMainPic(view, itemsDTO);
            appendSellOutStatus(view, itemsDTO);
        });
    }

    public void appendAdditionalSkuInfo(List<SearchedItemNew.ItemsNew> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }

        //查询sku基本信息（key=item.id）
        Map<Long, List<SkusDTO>> skuMap = findSkuMap(sourceList.stream().map(SearchedItemNew.ItemsNew::getId).collect(Collectors.toList()));

        //填充
        sourceList.forEach(view -> {
            List<SkusDTO> skusDTOS = skuMap.get(view.getId());
            if (CollectionUtils.isEmpty(skusDTOS)) {
                return;
            }

            appendPrice(view, skusDTOS.get(0));
            appendCrossedPrice(view, skusDTOS.get(0));
        });
    }

    private void appendMainPic(SearchedItemNew.ItemsNew target, ItemsDTO item) {
        if (target == null || item == null) {
            return;
        }

        if (!item.isExtraActivityValid()) {
            //活动不生效
            return;
        }

        //当前活动生效中，取活动图
        String activityMainImage = item.getExtra().get(ItemExtraIndex.activityMainImage.name());
        if (StringUtils.hasText(activityMainImage)) {
            target.setMainPic(imageUrlProcessor.restore(activityMainImage));
        }
    }

    private Map<Long, ItemsDTO> findItemMap(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }

        List<ItemsDTO> list = itemsGateway.selectIds(itemIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(ItemsDTO::getId, o -> o, (k1, k2) -> k1));
    }

    public Map<Long, List<SkusDTO>> findSkuMap(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }

        List<SkusDTO> list = skusGateway.findSkusByItemIds(itemIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(SkusDTO::getItemId));
    }

    private void appendSellOutStatus(SearchedItemNew.ItemsNew target, ItemsDTO item) {
        if (target == null || item == null || item.getSellOutStatus() == null) {
            return;
        }
        target.setSellOutStatus(item.getSellOutStatus());
    }


    private SearchRequest createQueryParamsBuild(ItemSearchParam param) {
        SearchRequest searchRequest = new SearchRequest("items");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        Long shopId = param.getShopId();
        if (shopId != null) {
            boolQuery.must(QueryBuilders.termQuery("shopId", shopId));
        }
        if (ObjUtil.isNotEmpty(param.getType())) {
            boolQuery.must(QueryBuilders.termQuery("type", param.getType())); // 明确使用 keyword
        }
        if (ObjUtil.isNotEmpty(param.getIds())) {
            boolean isMatch = param.getIds().contains("_");
            if (isMatch) {
                String[] ids = param.getIds().split("_");
                boolQuery.must(QueryBuilders.termsQuery("id", ids)); // 使用 terms 查询
            } else {
                boolQuery.must(QueryBuilders.termsQuery("id", param.getIds()));
            }
        }
        if (ObjUtil.isNotEmpty(param.getSsid()) && !Objects.equals(param.getSsid(), "-1")) {
            boolQuery.must(QueryBuilders.termQuery("ssid.keyword", param.getSsid()));
        }
        if (ObjUtil.isNotEmpty(param.getShopCatId())) {
            boolQuery.must(QueryBuilders.termQuery("shopCategoryIds", param.getShopCatId()));
        }
        if (StrUtil.isNotBlank(param.getKeyword())) {
            // 根据关键字模糊查询
            boolQuery.must(new BoolQueryBuilder()
                    .should(QueryBuilders.matchQuery("name", param.getKeyword())
                            .fuzziness("AUTO")
                            .prefixLength(1)));
        }
        searchSourceBuilder.query(boolQuery); // 设置组合后的查询

        // 排序
        searchSourceBuilder.sort("categoryIndex", SortOrder.ASC);
        searchSourceBuilder.sort("index", SortOrder.ASC);
        searchSourceBuilder.sort("updatedAt", SortOrder.DESC);
        if (StrUtil.isBlank(param.getIds())) {
            int from = (param.getPageNo() - 1) * param.getPageSize();
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(param.getPageSize());
        }
        searchRequest.source(searchSourceBuilder);
        log.info("查询es语句 {}", searchSourceBuilder);
        return searchRequest;
    }


    private void appendPrice(SearchedItemNew.ItemsNew target, SkusDTO sku) {
        if (target == null || sku == null) {
            return;
        }

        if (!sku.isExtraActivityValid()) {
            //活动不生效
            return;
        }

        //当前活动生效中，取活动价
        String activitySalesPrice = sku.getExtraMap().get(SkuExtraIndex.activitySalesPrice.name());
        if (StringUtils.hasText(activitySalesPrice)) {
            target.setPrice(Integer.parseInt(activitySalesPrice));
        }
    }

    private void appendCrossedPrice(SearchedItemNew.ItemsNew target, SkusDTO sku) {
        if (target == null || sku == null) {
            return;
        }

        if (CollectionUtils.isEmpty(sku.getExtraPrice())) {
            return;
        }

        Integer crossedPrice = sku.getExtraPrice().get(SkuExtraPriceIndex.crossedPrice.getCode());
        if (crossedPrice != null) {
            target.setCrossedPrice(crossedPrice);
        }
    }


    @Override
    public Long createItem(ItemAddOrUpdateRequest req) {
        if (!functionSwitch.getAllowCreateItem()) {
            throw new RuntimeException("禁止创建商品");
        }
        if (UserHelper.isNotLoggedIn()) {
            throw new RuntimeException("用户未登录");
        }
        CommonUser currentUser = UserHelper.getUser();
        ShopDTO shop = shopGateway.selectOne(new ShopQry().setId(currentUser.getShopId()).setStatus(1));
        // 自动填充一些公共参数
        autoFillCommonParam(req);
        // 校验请求参数
        validateCreateItemRequest(req, shop);
        ItemModel itemModel = buildItemModel(shop, req);
        log.info("需要保存的商品信息 {}", JSONUtil.toJsonStr(itemModel));
        return self.saveItemModel(itemModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveItemModel(ItemModel itemModel) {
        ItemsDTO item = itemModel.getItem();
        itemsGateway.insert(item);
        // 填充商品id
        Long itemId = item.getId();
        appendItemId(itemModel, itemId);
        ItemDetailsDTO itemDetail = itemModel.getItemDetail();
        itemDetailsGateway.insert(itemDetail);
        ItemAttributesDTO itemAttributes = itemModel.getItemAttributes();
        itemAttributesGateway.insert(itemAttributes);
        ItemDeliveryFeeDTO itemDeliveryFee = itemModel.getItemDeliveryFee();
        itemDeliveryFeeGateway.insert(itemDeliveryFee);
        if (CollUtil.isNotEmpty(itemModel.getShopCategoryItemList())) {
            List<ShopCategoryItemDTO> shopCategoryItemList = itemModel.getShopCategoryItemList();
            shopCategoryItemGateway.insertBatch(shopCategoryItemList);
        }
        if (CollUtil.isNotEmpty(itemModel.getItemsSpecificationDetailList())) {
            List<ItemsSpecificationDetailDTO> itemsSpecificationDetailList = itemModel.getItemsSpecificationDetailList();
            itemsSpecificationDetailGateway.insertBatch(itemsSpecificationDetailList);
        }
        log.info("填充商品id后的商品模型 {}", JSONUtil.toJsonStr(itemModel));
        saveOrUpdateSkuRelatedInfoList(itemModel.getSkuRelatedInfoList(), item);

        ItemSyncMessage itemSyncMessage = new ItemSyncMessage();
        itemSyncMessage.setItemId(itemId);
        itemSyncMessage.setType(ItemSyncMessage.Type.Create);
        itemSyncMessage.setCreateTime(new Date().getTime());
        String traceId = ApmTrace.apmTraceId();
        itemSyncMessage.setEventId(traceId);
        SpringMessage<ItemSyncMessage> mqMessage = new SpringMessage<>(itemSyncMessage);
        SendResult result = springRocketMQProducer.syncSend(RocketMQConstant.OLD_APP_ITEM_SYNC_TOPIC, mqMessage);
        if (SendStatus.SEND_OK.equals(result.getSendStatus())) {
            log.info("发送商品创建消息成功 商品id {}", itemId);
        }
        return itemId;
    }

    @Override
    public Boolean updateItem(ItemAddOrUpdateRequest req) {
        if (UserHelper.isNotLoggedIn()) {
            throw new RuntimeException("用户未登录");
        }
        CommonUser currentUser = UserHelper.getUser();
        ShopDTO shop = shopGateway.selectOne(new ShopQry().setId(currentUser.getShopId()).setStatus(1));
        validateUpdateItemRequest(req, shop);
        ItemModel itemModel = buildItemModel(shop, req);
        Long itemId = itemModel.getItem().getId();
        appendItemId(itemModel, itemId);
        return self.updateItemModel(itemModel);
    }

    @Override
    public ItemDetailResponse itemDetail(Long itemId) {
        if (UserHelper.isNotLoggedIn()) {
            throw new RuntimeException("用户未登录");
        }
        CommonUser currentUser = UserHelper.getUser();
        ShopDTO shop = shopGateway.selectOne(new ShopQry().setId(currentUser.getShopId()).setStatus(1));
        ItemsDTO item = itemsGateway.selectById(itemId);
        validateItemDetailRequest(item, shop);
        return fillItemDetailResponse(item);
    }

    @Override
    public SkuDetailResponse skuInfoByItemId(Long itemId, Boolean hasGbActivity) {
        SkuDetailPageTemplate skuDetailPageTemplate = skuDetailPageTemplateFactory.getByTag(hasGbActivity);
        return skuDetailPageTemplate.detail(itemId);
    }


    @Override
    public FindActivityInfoResponse findActivityInfo(SkuFindActivityInfoRequest request) {
        Long shopId = ShopHelper.getId();
        // 查询 商家+商品+SKu 是否参与了活动
        GbActivityConfigSkuDTO gbActivityConfigSku = gbActivityConfigSkuGateway.getOneInvalidSkuInActivity(request.getSkuId(), shopId);

        FindActivityInfoResponse response = new FindActivityInfoResponse();
        if (gbActivityConfigSku == null) {
            response.setHasGbActivity(false);
        } else {
            response.setHasGbActivity(true);
        }
        return response;
    }

    @Override
    public SkuDetailResponse skuInfoByItemId(Long itemId) {
        ItemsDTO item = itemsGateway.selectById(itemId);
        if (ObjUtil.isEmpty(item)) {
            throw new RuntimeException("未找到商品信息");
        }
        ItemSpecWithSkuDto itemSpecWithSkuDto = itemsGateway.findItemSpecWithSkuDtoByItemId(itemId);
        return BeanUtil.copyProperties(itemSpecWithSkuDto, SkuDetailResponse.class);
    }

    @Override
    public PageDTO<ChooseComboProductPageResponse> chooseComboProductPage(ChangeComboProductPageRequest req) {
        Long shopId = ShopHelper.getId();
        if (ObjUtil.isEmpty(shopId)) {
            throw new RuntimeException("未获取到店铺id");
        }
        PageDTO<ChooseComboProductPageResponse> response = new PageDTO<>();
        PageDTO.Page page = new PageDTO.Page();
        page.setCurrentPage(Long.valueOf(req.getCurrentPage()));
        page.setPageSize(Long.valueOf(req.getPageSize()));
        ChooseComboProductQuery param = BeanUtil.copyProperties(req, ChooseComboProductQuery.class);
        param.setOffset((req.getCurrentPage() - 1) * req.getPageSize());
        param.setShopId(shopId);
        Long totalCount = itemsGateway.chooseComboProductCount(param);
        long totalPage = totalCount % page.getPageSize() == 0 ? totalCount / page.getPageSize() : totalCount / page.getPageSize() + 1;
        page.setTotalCount(totalCount);
        page.setTotalPage(totalPage);
        response.setPage(page);
        if (totalCount == 0L) {
            response.setDataList(Collections.emptyList());
            return response;
        }
        List<ItemsDTO> itemsList = itemsGateway.chooseComboProductList(param);
        List<ChooseComboProductPageResponse> dataList = itemsList.stream().map(item -> {
            ChooseComboProductPageResponse entity = BeanUtil.copyProperties(item, ChooseComboProductPageResponse.class);
            entity.setTradeTypeDesc(ItemTradeTypeEnum.getDescByCode(item.getIsBonded()));
            entity.setWhetherExistSpec(itemsSpecificationDetailGateway.count(new ItemsSpecificationDetailQry().setItemId(item.getId()).setShopId(shopId)) > 0);
            entity.setMainImage(imageUrlProcessor.restore(item.getMainImage()));
            List<SkusDTO> skuList = skusGateway.findSkuListByItemId(item.getId());
            entity.setSkus(BeanUtil.copyToList(skuList, OptionalSkuDto.class));
            return entity;
        }).collect(Collectors.toList());
        response.setDataList(dataList);
        return response;
    }

    private ItemDetailResponse fillItemDetailResponse(ItemsDTO item) {
        ItemDetailResponse response = new ItemDetailResponse();
        Long itemId = item.getId();
        ItemParam itemParam = BeanUtil.copyProperties(item, ItemParam.class);
        itemParam.setMainImage(imageUrlProcessor.restore(item.getMainImage()));
        if (ObjUtil.isNotEmpty(itemParam.getBrandId()) && itemParam.getBrandId().equals(-1L)) {
            itemParam.setBrandId(null);
        }
        response.setItem(itemParam);
        List<BackCategoriesDTO> backCategoriesList = backCategoriesGateway.getBackCategoriesByLastCategoryId(item.getCategoryId());
        response.setBackCategoryList(BeanUtil.copyToList(backCategoriesList, ItemBackCategoriesParam.class));
        List<ShopCategoryItemDTO> shopCategoryItemList = shopCategoryItemGateway.selectList(new ShopCategoryItemQry().setItemId(itemId));
        List<Long> shopCategoryIdList = shopCategoryItemList.stream().map(ShopCategoryItemDTO::getShopCategoryId).filter(shopCategoryItemId -> shopCategoryItemId != 0L).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(shopCategoryIdList)) {
            List<ShopCategoriesDTO> shopCategoryList = shopCategoriesGateway.selectByIds(shopCategoryIdList);
            response.setShopCategoryList(BeanUtil.copyToList(shopCategoryList, ShopCategoryResponse.class));
        }
        List<ItemsSpecificationDTO> itemsSpecificationList = itemsSpecificationDetailGateway.getListByAllSpecDetails(itemId);
        response.setItemSpecificationParams(BeanUtil.copyToList(itemsSpecificationList, ItemSpecificationParam.class));
        ItemDetailsDTO itemDetailsDTO = itemDetailsGateway.selectByItemId(itemId);
        ItemDetailParam itemDetail = BeanUtil.copyProperties(itemDetailsDTO, ItemDetailParam.class);
        if (StrUtil.isNotBlank(itemDetailsDTO.getDetailJson())) {
            log.info("商品详情 {}", itemDetailsDTO.getDetailJson());
            List<String> imageList = new ArrayList<>();
            List<String> images = JSONUtil.toList(itemDetailsDTO.getDetailJson(), String.class);
            for (String image : images) {
                imageList.add(imageUrlProcessor.restore(image));
            }
            itemDetail.setImageList(imageList);
        }
        if (StrUtil.isNotBlank(itemDetailsDTO.getImagesJson())) {
            log.info("商品辅图 {}", itemDetailsDTO.getImagesJson());
            List<ImageInfo> images = JSONUtil.toList(itemDetailsDTO.getImagesJson(), ImageInfo.class);
            for (ImageInfo image : images) {
                image.setUrl(imageUrlProcessor.restore(image.getUrl()));
            }
            itemDetail.setImages(images);
        }
        response.setItemDetail(itemDetail);
        ItemDeliveryFeeDTO itemDeliveryFeeDTO = itemDeliveryFeeGateway.findByItemId(itemId);
        response.setItemDeliveryFee(BeanUtil.copyProperties(itemDeliveryFeeDTO, ItemDeliveryFeeParam.class));

        ParanaItemAttrRequest request = new ParanaItemAttrRequest();
        request.setItemId(itemId);
        ParanaItemAttrResponse paranaItemAttrResponse = paranaClientActuator.get(request, ParanaItemAttrResponse.class);
        log.info("根据老项目接口 获取商品属性信息 {}", JSONUtil.toJsonStr(paranaItemAttrResponse));
        if (paranaItemAttrResponse.getCode() == 0) {
            Object otherAttrs = JSONUtil.parseObj(paranaItemAttrResponse.getData()).get("otherAttrs");
            response.setOtherAttrs(otherAttrs);

        }
        // 添加sku相关信息
        appendSkuRelatedInfo(response, itemId);
        return response;
    }

    private void appendSkuRelatedInfo(ItemDetailResponse response, Long itemId) {
		Integer itemType = response.getItem().getType();
		List<SkusDTO> skusList = skusGateway.findSkuListByItemId(itemId);
		ItemsEditorPrivilege itemsEditorPrivilege = new ItemsEditorPrivilege();
		// 默认可以编辑
		boolean whetherEditor = true;
		String reason = "";
		List<SkuWithCustomParam> skuWithCustoms = new ArrayList<>();
		for (SkusDTO sku : skusList) {
			int isCashGift = 0;
			Long cashGiftUseTimeStart = null;
			Long cashGiftUseTimeEnd = null;
			Long skuId = sku.getId();
			SkuWithCustomParam skuWithCustomParam = new SkuWithCustomParam();
			if (itemType.equals(ItemTypeEnum.NORMAL.getCode())) {
				SkuParam skuParam = BeanUtil.copyProperties(sku, SkuParam.class);
				skuParam.setSkuId(skuParam.getId());
				IntermediateInfoDTO normalIntermediateInfo = intermediateInfoGateway.selectOne(new IntermediateInfoQry().setThirdId(skuId).setType(ThirdIntermediateType.SKU.value()).setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode()));
				if (ObjUtil.isNotEmpty(normalIntermediateInfo)) {
					if (ObjUtil.isNotEmpty(normalIntermediateInfo.getIsCashGift()) && normalIntermediateInfo.getIsCashGift() == 1) {
						// 设置是否使用福豆
						isCashGift = 1;
						skuParam.setMaxDeduction(normalIntermediateInfo.getMaxDeduction());
						skuParam.setCashGiftUnit(normalIntermediateInfo.getUnit());
						if (ObjUtil.isNotEmpty(normalIntermediateInfo.getCashGiftUseTimeStart()) && ObjUtil.isNotEmpty(normalIntermediateInfo.getCashGiftUseTimeEnd())) {
							// 设置福豆使用时间
							cashGiftUseTimeStart = normalIntermediateInfo.getCashGiftUseTimeStart();
							cashGiftUseTimeEnd = normalIntermediateInfo.getCashGiftUseTimeEnd();
						}
					}
					response.setIsCashGift(isCashGift);
					response.setCashGiftUseTimeStart(cashGiftUseTimeStart);
					response.setCashGiftUseTimeEnd(cashGiftUseTimeEnd);
					int isCommission = 0;
					if (ObjUtil.isNotEmpty(normalIntermediateInfo.getIsCommission()) && normalIntermediateInfo.getIsCommission() == 1) {
						isCommission = 1;
						response.setServiceProviderFee(normalIntermediateInfo.getServiceProviderFee());
						response.setServiceProviderRate(normalIntermediateInfo.getServiceProviderRate());
						response.setFirstRate(normalIntermediateInfo.getFirstRate());
						response.setFirstFee(normalIntermediateInfo.getFirstFee());
						response.setSecondRate(normalIntermediateInfo.getSecondRate());
						response.setSecondFee(normalIntermediateInfo.getSecondFee());
					}
					response.setIsCommission(isCommission);
                }
                IntermediateInfoDTO activityIntermediateInfo = intermediateInfoGateway.selectOne(new IntermediateInfoQry().setThirdId(skuId).setType(ThirdIntermediateType.SKU.value()).setMatchingType(IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode()));
                if (ObjUtil.isNotEmpty(activityIntermediateInfo)) {
                    SkuCommissionConfigParam activityCommissionConfig = BeanUtil.copyProperties(activityIntermediateInfo, SkuCommissionConfigParam.class);
                    activityCommissionConfig.setMatchingStartTimeString(DateUtil.formatDateTime(activityIntermediateInfo.getMatchingStartTime()));
                    activityCommissionConfig.setMatchingEndTimeString(DateUtil.formatDateTime(activityIntermediateInfo.getMatchingEndTime()));
                    activityCommissionConfig.setFlag(1);
                    response.setActivityCommissionConfig(activityCommissionConfig);
                }

				List<SkuComboRelationDTO> skuComboRelationList = skuComboRelationGateway.getValidSkuComboRelationByComboSkuIdList(Collections.singletonList(skuId));
				if (CollUtil.isNotEmpty(skuComboRelationList)) {
					whetherEditor = false;
					SkuComboRelationDTO skuComboRelationDTO = skuComboRelationList.get(0);
					reason = "商品正在参加已关联组合商品Id：" + skuComboRelationDTO.getComboItemId() + "（活动类型：组合商品），不支持修改规格信息";
				}

				GbActivityConfigSkuQry activityQuery = new GbActivityConfigSkuQry();
				activityQuery.setSkuId(skuId);
				activityQuery.setShopId(sku.getShopId());
				List<GbActivityConfigSkuDTO> activityList = gbActivityConfigSkuGateway.selectListOnUse(activityQuery);
				if (CollUtil.isNotEmpty(activityList)) {
					whetherEditor = false;
					reason = "该SKU:" + skuId + "参加了其他活动，不支持修改规格信息";
				}
				List<SkusSpecificationRelevanceDTO> skusSpecificationRelevanceList = skusSpecificationRelevanceGateway.selectList(new SkusSpecificationRelevanceQry().setSkuId(skuId));
				if (ObjUtil.isNotEmpty(skusSpecificationRelevanceList)) {
					skuParam.setSpecDetails(skusSpecificationRelevanceList.stream().sorted(Comparator.comparing(SkusSpecificationRelevanceDTO::getSequence)).map(SkusSpecificationRelevanceDTO::getSpecDetailFrontId).collect(Collectors.toList()));
				}
				skuWithCustomParam.setSku(skuParam);
			}

			if (itemType.equals(ItemTypeEnum.BUNDLE.getCode())) {
				IntermediateInfoDTO normalIntermediateInfo = intermediateInfoGateway.selectOne(new IntermediateInfoQry().setThirdId(skuId).setType(ThirdIntermediateType.SKU.value()).setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode()));
				SkuParam skuParam = BeanUtil.copyProperties(sku, SkuParam.class);
				skuParam.setSkuId(skuParam.getId());
				// 组合商品里的商品列表
				skuParam.setSkuComboList(getSkuComboList(skuId));
                if (ObjUtil.isNotEmpty(normalIntermediateInfo)) {
                    // 组合品
                    if (ObjUtil.isNotEmpty(normalIntermediateInfo.getCashGiftUseTimeStart()) && ObjUtil.isNotEmpty(normalIntermediateInfo.getCashGiftUseTimeEnd())) {
                        // 设置福豆使用时间
                        cashGiftUseTimeStart = normalIntermediateInfo.getCashGiftUseTimeStart();
                        cashGiftUseTimeEnd = normalIntermediateInfo.getCashGiftUseTimeEnd();
                    }
                    if (ObjUtil.isNotEmpty(normalIntermediateInfo.getIsCashGift()) && normalIntermediateInfo.getIsCashGift() == 1) {
                        // 设置是否使用福豆
                        isCashGift = 1;
                        skuParam.setComboCashGiftMaxDeduction(normalIntermediateInfo.getMaxDeduction());
                        skuParam.setComboCashGiftUnit(normalIntermediateInfo.getUnit());
                    }
                }
				skuParam.setComboCashGiftSwitch(isCashGift);
				skuParam.setComboCashGiftUseTimeStart(cashGiftUseTimeStart);
				skuParam.setComboCashGiftUseTimeEnd(cashGiftUseTimeEnd);
				List<SkusSpecificationRelevanceDTO> skusSpecificationRelevanceList = skusSpecificationRelevanceGateway.selectList(new SkusSpecificationRelevanceQry().setSkuId(skuId));
				if (ObjUtil.isNotEmpty(skusSpecificationRelevanceList)) {
					skuParam.setSpecDetails(skusSpecificationRelevanceList.stream().sorted(Comparator.comparing(SkusSpecificationRelevanceDTO::getSequence)).map(SkusSpecificationRelevanceDTO::getSpecDetailFrontId).collect(Collectors.toList()));
				}
				skuWithCustomParam.setSku(skuParam);
			}
            SkuCustomDTO skuCustomDTO = skuCustomGateway.selectOne(new SkuCustomQry().setSkuId(skuId));
            skuWithCustomParam.setSkuCustom(BeanUtil.copyProperties(skuCustomDTO, SkuCustomParam.class));
            skuWithCustoms.add(skuWithCustomParam);
        }
        response.setSkuWithCustoms(skuWithCustoms);
        itemsEditorPrivilege.setWhetherEditor(whetherEditor);
        itemsEditorPrivilege.setReason(reason);
        response.setItemsEditorPrivilege(itemsEditorPrivilege);
    }

    private List<SkuComboParam> getSkuComboList(Long skuId) {
        List<SkuComboRelationDTO> skuComboRelationList = skuComboRelationGateway.selectList(new SkuComboRelationQry().setSkuId(skuId));
        if (CollUtil.isEmpty(skuComboRelationList)) {
            return Collections.emptyList();
        }
        return skuComboRelationList.stream().map(skuComboRelation -> {
            SkuComboParam entity = BeanUtil.copyProperties(skuComboRelation, SkuComboParam.class);
            Long comboSkuId = entity.getComboSkuId();
            SkusDTO sku = skusGateway.getById(comboSkuId);
            entity.setComboSkuPrice(sku.getPrice());
            entity.setComboSkuStockQuantity(sku.getStockQuantity());
            entity.setComboSkuSpecification(sku.getSpecification());
            ItemsDTO item = itemsGateway.selectById(skuComboRelation.getComboItemId());
            entity.setComboItemMainImage(imageUrlProcessor.restore(item.getMainImage()));
            return entity;
        }).collect(Collectors.toList());
    }


    private void validateItemDetailRequest(ItemsDTO item, ShopDTO shop) {
        if (ObjUtil.isEmpty(item)) {
            throw new RuntimeException("商品不存在");
        }
        if (!item.getShopId().equals(shop.getId())) {
            throw new RuntimeException("该商品不属于您的店铺");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateItemModel(ItemModel itemModel) {
        ItemsDTO item = itemModel.getItem();
        Long itemId = item.getId();
        itemsGateway.update(item);
        ItemDetailsDTO itemDetail = itemModel.getItemDetail();
        itemDetailsGateway.update(itemDetail);
        ItemDeliveryFeeDTO itemDeliveryFee = itemModel.getItemDeliveryFee();
        ItemDeliveryFeeDTO DbItemDeliveryFee = itemDeliveryFeeGateway.selectOne(new ItemDeliveryFeeQry().setItemId(itemId));
        if (ObjUtil.isNotEmpty(DbItemDeliveryFee)) {
            itemDeliveryFeeGateway.updateById(DbItemDeliveryFee.getId(), itemDeliveryFee);
        } else {
            itemDeliveryFeeGateway.insert(itemDeliveryFee);
        }
        // 删除
        shopCategoryItemGateway.delete(new ShopCategoryItemQry().setItemId(itemId));
        List<ShopCategoryItemDTO> shopCategoryItemList = itemModel.getShopCategoryItemList();
        if (CollUtil.isNotEmpty(shopCategoryItemList)) {
            shopCategoryItemGateway.insertBatch(shopCategoryItemList);
        }
        // 删除原有规格
        itemsSpecificationDetailGateway.delete(new ItemsSpecificationDetailQry().setItemId(itemId));
        List<ItemsSpecificationDetailDTO> itemsSpecificationDetailList = itemModel.getItemsSpecificationDetailList();
        if (CollUtil.isNotEmpty(itemsSpecificationDetailList)) {
            itemsSpecificationDetailGateway.insertBatch(itemsSpecificationDetailList);
        }
        ItemAttributesDTO itemAttributes = itemModel.getItemAttributes();
        ItemAttributesDTO itemAttributesDTO = itemAttributesGateway.selectOne(new ItemAttributesQry().setItemId(itemId));
        itemAttributesDTO.setOtherAttributes(itemAttributes.getOtherAttributes());
        itemAttributesGateway.update(itemAttributesDTO);
        saveOrUpdateSkuRelatedInfoList(itemModel.getSkuRelatedInfoList(), item);
        itemsGateway.sendItemSyncMessage(itemId);
        return true;
    }


    private void validateUpdateItemRequest(ItemAddOrUpdateRequest req, ShopDTO shop) {
        if (ObjUtil.isEmpty(shop)) {
            throw new RuntimeException("商家不存在");
        }
        if (req.getSkuWithCustoms().size() > 125) {
            throw new RuntimeException("规格数量不能超过125个");
        }
        ItemParam item = req.getItem();
		Long itemId = item.getId();
		ItemsDTO itemsDTO = itemsGateway.selectById(itemId);
        if (ObjUtil.isEmpty(itemsDTO)) {
            throw new RuntimeException("商品不存在");
        }
        if (!shop.getId().equals(itemsDTO.getShopId())) {
            throw new RuntimeException("无法修改该商品");
        }

        // 提取已有的规格属性并转换为Set
        Set<String> existingSpecSet = new HashSet<>();

        // 提取传入的规格属性并转换为Set
        Set<String> inputSpecSet = new HashSet<>();

        if (CollUtil.isNotEmpty(req.getItemSpecificationParams())) {
            // 有规格的商品进行修改的时候 进行比较

            // 获取数据库已存在的规格属性列表
            List<ItemsSpecificationDTO> existingSpecDetails = itemsSpecificationDetailGateway.getListByAllSpecDetails(itemId);

            if (CollUtil.isNotEmpty(existingSpecDetails)) {
                for (ItemsSpecificationDTO specParam : existingSpecDetails) {
                    for (ItemsSpecificationDetailDTO detailParam : specParam.getItemSpecificationDetailParams()) {
                        if (StrUtil.isNotBlank(detailParam.getName())) {
                            existingSpecSet.add(detailParam.getFrontId() + ":" + detailParam.getName());
                        }
                    }
                }
            }

            for (ItemSpecificationParam specParam : req.getItemSpecificationParams()) {
                for (ItemSpecificationDetailParam detailParam : specParam.getItemSpecificationDetailParams()) {
                    if (StrUtil.isNotBlank(detailParam.getName())) {
                        inputSpecSet.add(detailParam.getFrontId() + ":" + detailParam.getName());
                    }
                }
            }
            // 比较两者是否一致
            if (!existingSpecSet.equals(inputSpecSet)) {
                List<SkuComboRelationDTO> skuComboRelationList = skuComboRelationGateway.selectList(new SkuComboRelationQry().setComboItemId(itemId));
                if (CollUtil.isNotEmpty(skuComboRelationList)) {
                    SkuComboRelationDTO skuComboRelationDTO = skuComboRelationList.get(0);
                    throw new RuntimeException("该商品：" + skuComboRelationDTO.getComboItemName() + "参加了组合品活动，不支持修改规格信息");
                }
                GbActivityConfigSkuQry activityQuery = new GbActivityConfigSkuQry();
                activityQuery.setItemId(itemId);
                activityQuery.setShopId(itemsDTO.getShopId());
                List<GbActivityConfigSkuDTO> activityList = gbActivityConfigSkuGateway.selectListOnUse(activityQuery);
                if (CollUtil.isNotEmpty(activityList)) {
                    throw new RuntimeException("该商品：" + itemsDTO.getName() + "参加了其他活动，不支持修改规格信息");
                }
            }

        }


        if (ObjUtil.isNotEmpty(req.getIsCashGift()) && req.getIsCashGift() == 1) {
            // 校验福豆相关参数
            validateCashGift(req.getSkuWithCustoms());
        }
        if (itemsDTO.getType().equals(ItemTypeEnum.NORMAL.getCode())) {
            //  校验单品的编辑权限
            validateSkuEditorPrivilege(req.getSkuWithCustoms(), itemsDTO);
        }
        if (itemsDTO.getType().equals(ItemTypeEnum.BUNDLE.getCode())) {
            //  组合品校验
            validateCombo(req.getSkuWithCustoms());
        }
        // sku相关校验
        validateSkuInfo(req.getSkuWithCustoms(), req.getItem());
    }

    /**
     * 校验sku相关信息
     */
    private void validateSkuInfo(List<SkuWithCustomParam> skuWithCustoms, ItemParam itemParam) {
        for (SkuWithCustomParam skuWithCustom : skuWithCustoms) {
            SkuParam sku = skuWithCustom.getSku();
            if (ObjUtil.isEmpty(sku)) {
                throw new RuntimeException("sku信息不能为空");
            }
            if (itemParam.getType().equals(ItemTypeEnum.NORMAL.getCode()) && StrUtil.isBlank(sku.getOuterSkuId())) {
                throw new RuntimeException("sku商家编码不能为空");
            }
            if (StrUtil.isNotBlank(sku.getSkuId())) {
                if (!sku.getSkuId().contains("-")) {
                    // skuId 不为空 则为修改
                    SkusDTO dbSkus = skusGateway.getById(Long.valueOf(sku.getSkuId()));
                    if (!dbSkus.getVersion().equals(sku.getVersion())) {
                        throw new RuntimeException("当前编辑已失效，请重新编辑");
                    }
                }
            }
        }
    }

    private void validateSkuEditorPrivilege(List<SkuWithCustomParam> skuWithCustoms, ItemsDTO itemsDTO) {
        Long itemId = itemsDTO.getId();
        for (SkuWithCustomParam skuWithCustom : skuWithCustoms) {
			SkuParam needUpdateSku = skuWithCustom.getSku();
			String comboSkuIdStr = needUpdateSku.getSkuId();
            if (StrUtil.isBlank(comboSkuIdStr)) {
                continue;
            }
            long comboSkuId = Long.parseLong(comboSkuIdStr);
            List<SkuComboRelationDTO> skuComboRelationList = skuComboRelationGateway.selectList(new SkuComboRelationQry().setComboItemId(itemId));
            if (CollUtil.isNotEmpty(skuComboRelationList)) {
                SkusDTO skus = skusGateway.getById(comboSkuId);
                if (skus.getStatus().equals(SkuStatusEnum.ON_SHELF.getCode())) {
                    if (ObjUtil.isNotEmpty(needUpdateSku.getStatus()) && needUpdateSku.getStatus().equals(SkuStatusEnum.OFF_SHELF.getCode())) {
                        throw new RuntimeException("该商品属于其他组合商品的子品，暂不支持下架操作");
                    }
                }
            }
            GbActivityConfigSkuQry activityQuery = new GbActivityConfigSkuQry();
            activityQuery.setItemId(itemId);
            activityQuery.setShopId(itemsDTO.getShopId());
            List<GbActivityConfigSkuDTO> activityList = gbActivityConfigSkuGateway.selectListOnUse(activityQuery);
            if (CollUtil.isNotEmpty(activityList)) {
                SkusDTO skus = skusGateway.getById(comboSkuId);
                if (skus.getStatus().equals(SkuStatusEnum.ON_SHELF.getCode())) {
                    if (needUpdateSku.getStatus().equals(SkuStatusEnum.OFF_SHELF.getCode())) {
                        throw new RuntimeException("该商品已绑定正在进行的活动，暂不支持下架操作");
                    }
                }
            }
        }
    }

    private void validateCombo(List<SkuWithCustomParam> skuWithCustoms) {
        for (SkuWithCustomParam skuWithCustom : skuWithCustoms) {
            SkuParam sku = skuWithCustom.getSku();
            // 校验组合品的福豆
            validateComboCashGift(sku);
            // 校验组合品下的商品状态
            validateComboRelation(sku);
        }

    }

    private void validateComboRelation(SkuParam sku) {
        for (SkuComboParam skuComboParam : sku.getSkuComboList()) {
            Long comboItemId = skuComboParam.getComboItemId();
            Long comboSkuId = skuComboParam.getComboSkuId();
            SkusDTO skusDTO = skusGateway.getById(comboSkuId);
            if (ObjUtil.isEmpty(skusDTO)) {
                String errorMsg = "";
                errorMsg += "请重新添加" + skuComboParam.getComboItemName() + "，当前：" + skuComboParam.getComboItemName() + "无法创建组合品";
                throw new RuntimeException(errorMsg);
            }
            GbActivityConfigSkuQry activityQuery = new GbActivityConfigSkuQry();
            activityQuery.setItemId(comboItemId);
            List<GbActivityConfigSkuDTO> activityList = gbActivityConfigSkuGateway.selectListOnUse(activityQuery);
            if (CollUtil.isNotEmpty(activityList)) {
                throw new RuntimeException("该商品：" + skuComboParam.getComboItemName() + "参加了其他活动，不支持参与组合品活动");
            }
        }
    }

    private void validateComboCashGift(SkuParam sku) {
        if (ObjUtil.isNotEmpty(sku.getComboCashGiftSwitch()) && sku.getComboCashGiftSwitch() == 1) {
            if (ObjUtil.isEmpty(sku.getComboCashGiftMaxDeduction())) {
                throw new RuntimeException("商品可使用福豆最大抵扣金额/比例不能为空");
            }
            if (ObjUtil.isEmpty(sku.getComboCashGiftUnit())) {
                throw new RuntimeException("商品可使用福豆抵扣单位不能为空");
            }
            if (sku.getComboCashGiftUnit().equals(CashGiftUnitEnum.YUAN.getCode())) {
                // 元
                BigDecimal maxDeduction = sku.getComboCashGiftMaxDeduction().multiply(BigDecimal.valueOf(100));
                maxDeduction = maxDeduction.multiply(beanConvertRate);//福豆按照比例转换成金额
                if (maxDeduction.intValue() >= sku.getPrice()) {
                    throw new RuntimeException("商品可使用福豆最大抵扣金额/比例不能大于等于商品价格");
                }
            } else if (sku.getComboCashGiftUnit().equals(CashGiftUnitEnum.PERCENT.getCode())) {
                if (sku.getComboCashGiftMaxDeduction().compareTo(BigDecimal.valueOf(100)) >= 0) {
                    throw new RuntimeException("商品可使用福豆最大抵扣金额/比例不能大于等于100");
                }
            } else {
                throw new RuntimeException("商品可使用福豆抵扣单位有误");
            }
        }

    }

    private void saveOrUpdateSkuRelatedInfoList(List<SkuRelatedInfoDto> skuRelatedInfoList, ItemsDTO item) {
        List<Long> skuIdList = new ArrayList<>();
        List<SkuHistory> skuHistories = new ArrayList<>();
        for (SkuRelatedInfoDto skuRelatedInfoDto : skuRelatedInfoList) {
            SkusDTO sku = skuRelatedInfoDto.getSku();
            Long skuId = sku.getId();
            SkuHistory skuHistory = new SkuHistory();
            if (skuId != null) {
                // 构建历史数据
                SkuHistoryData beforeData = buildSkuHistoryData(skuId);
                skuHistory.setBeforeData(beforeData);
            }
            sku.setItemId(item.getId());
            skusGateway.insertOrUpdate(sku);
            skuId = sku.getId();
            skuIdList.add(skuId);
            if (item.getIsBonded() == ItemTradeTypeEnum.BONDED.getCode()) {
                SkuCustomDTO skuCustom = skuRelatedInfoDto.getSkuCustom();
                skuCustom.setSkuId(skuId);
                skuCustomGateway.insertOrUpdate(skuCustom);
            }
            intermediateInfoGateway.delete(new IntermediateInfoQry().setThirdId(skuId).setType(ThirdIntermediateType.SKU.getValue()));
			List<IntermediateInfoDTO> intermediateInfoList = skuRelatedInfoDto.getIntermediateInfoList();
			for (IntermediateInfoDTO intermediateInfoDTO : intermediateInfoList) {
				intermediateInfoDTO.setThirdId(skuId);
                intermediateInfoGateway.insertOrUpdate(intermediateInfoDTO);
			}
            List<SkusSpecificationRelevanceDTO> skusSpecificationRelevanceList = skuRelatedInfoDto.getSkusSpecificationRelevanceList();
            if (CollUtil.isNotEmpty(skusSpecificationRelevanceList)) {
                for (SkusSpecificationRelevanceDTO skusSpecificationRelevanceDTO : skusSpecificationRelevanceList) {
                    skusSpecificationRelevanceDTO.setSkuId(skuId);
                }
                skusSpecificationRelevanceGateway.insertOrUpdateBatch(skusSpecificationRelevanceList);
            }
            // 组合商品 关联关系
            List<SkuComboRelationDTO> skuComboRelationList = skuRelatedInfoDto.getSkuComboRelationList();
            if (CollUtil.isNotEmpty(skuComboRelationList)) {
                for (SkuComboRelationDTO skuComboRelationDTO : skuComboRelationList) {
                    skuComboRelationDTO.setId(null);
                    skuComboRelationDTO.setShopId(sku.getShopId());
                    skuComboRelationDTO.setSkuId(skuId);
                }
                skuComboRelationGateway.insertOrUpdateBatch(skuComboRelationList);
            }

            if (item.getType().equals(ItemTypeEnum.BUNDLE.getCode())) {
                // 动态组合品库存计算一下
                Integer stockQuantity = skuStockHandle.dynamicCalculateSkuStockQuantity(skuId);
                sku.setStockQuantity(stockQuantity);
                skusGateway.update(sku);
            }
            // 更新redis当中的sku库存（新增）
            skuStockHandle.updateCacheStock(skuId, sku.getStockQuantity());
            if (skuId != null) {
				skuHistory.setShopId(sku.getShopId());
				skuHistory.setSkuId(skuId);
				skuHistory.setCreatedTime(System.currentTimeMillis());
                // 构建更新后当前数据
                SkuHistoryData currentData = buildSkuHistoryData(skuId);
				skuHistory.setCurrentData(currentData);
                skuHistory.setVersion(sku.getVersion());
                skuHistories.add(skuHistory);
            }
        }
        List<SkusDTO> skusDTOList = skusGateway.findSkuListByItemId(item.getId());
        List<Long> needDeleteSkuIdList = skusDTOList.stream().map(SkusDTO::getId).filter(id -> !skuIdList.contains(id)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(needDeleteSkuIdList)) {
            skusGateway.delete(needDeleteSkuIdList);
        }
        if (item.getStatus().equals(ItemStatusEnum.ON_SHELF.getCode())) {
            // 商品是上架状态下
            List<SkusDTO> onShelfSkuList = skusDTOList.stream().filter(sku -> sku.getStatus().equals(SkuStatusEnum.ON_SHELF.getCode())).collect(Collectors.toList());
            if (CollUtil.isEmpty(onShelfSkuList)) {
                // 当前上架的sku列表为空的时候
                throw new RuntimeException("至少有一个sku处于上架状态");
            }
        }

        // 保存sku历史数据 需要放在最后
        if (CollUtil.isNotEmpty(skuHistories)) {
            skuHistoryGateway.insertBatch(skuHistories);
        }
    }

    private SkuHistoryData buildSkuHistoryData(Long skuId) {
        SkusDTO sku = skusGateway.getById(skuId);
        SkuHistoryData skuHistoryData = BeanUtil.copyProperties(sku, SkuHistoryData.class);
        Long itemId = skuHistoryData.getItemId();
        ItemsDTO itemsDTO = itemsGateway.getById(itemId);
        String name = itemsDTO.getName();
        if (StrUtil.isNotBlank(itemsDTO.getSpecification())) {
            name += "(" + itemsDTO.getSpecification() + ")";
        }
        skuHistoryData.setName(name);
        boolean isBonded = itemsDTO.getIsBonded() == ItemTradeTypeEnum.BONDED.getCode();
        if (isBonded) {
            // 保存保税信息
            SkuCustomDTO skuCustomDTO = skuCustomGateway.findNormalBySkuId(skuId);
            skuHistoryData.setSkuCustomHistory(BeanUtil.copyProperties(skuCustomDTO, SkuCustomHistory.class));
        }
        List<IntermediateInfoDTO> intermediateInfoList = intermediateInfoGateway.findAllByThirdAndType(skuId, ThirdIntermediateType.SKU.getValue());
        skuHistoryData.setSkuIntermediateInfoHistories(BeanUtil.copyToList(intermediateInfoList, SkuIntermediateInfoHistory.class));
        List<SkusSpecificationRelevanceDTO> skusSpecificationRelevanceList = skusSpecificationRelevanceGateway.selectList(new SkusSpecificationRelevanceQry().setSkuId(skuId));
        if (CollUtil.isNotEmpty(skusSpecificationRelevanceList)) {
            skuHistoryData.setSkusSpecificationRelevanceHistories(BeanUtil.copyToList(skusSpecificationRelevanceList, SkusSpecificationRelevanceHistory.class));
        }
        if (itemsDTO.getType() == ItemTypeEnum.BUNDLE.getCode()) {
            List<SkuComboRelationDTO> skuComboRelationList = skuComboRelationGateway.selectList(new SkuComboRelationQry().setSkuId(skuId));
            List<SkuComboRelationHistory> skuComboRelationHistories = BeanUtil.copyToList(skuComboRelationList, SkuComboRelationHistory.class);
            for (SkuComboRelationHistory skuComboRelationHistory : skuComboRelationHistories) {
                if (isBonded) {
                    // 保存保税信息
                    SkuCustomDTO skuCustomDTO = skuCustomGateway.findNormalBySkuId(skuId);
                    skuComboRelationHistory.setComboSkuCustomHistory(BeanUtil.copyProperties(skuCustomDTO, SkuCustomHistory.class));
                }
            }
            skuHistoryData.setSkuComboRelationHistories(skuComboRelationHistories);
        }
        return skuHistoryData;

    }

    private void appendItemId(ItemModel itemModel, Long itemId) {
        itemModel.getItemDetail().setItemId(itemId);
        itemModel.getItemDeliveryFee().setItemId(itemId);
        if (CollUtil.isNotEmpty(itemModel.getShopCategoryItemList())) {
            for (ShopCategoryItemDTO shopCategoryItemDTO : itemModel.getShopCategoryItemList()) {
                shopCategoryItemDTO.setItemId(itemId);
            }
        }
        if (CollUtil.isNotEmpty(itemModel.getItemsSpecificationDetailList())) {
            for (ItemsSpecificationDetailDTO itemsSpecificationDetailDTO : itemModel.getItemsSpecificationDetailList()) {
                itemsSpecificationDetailDTO.setItemId(itemId);
            }
        }
        itemModel.getItemAttributes().setItemId(itemId);
    }

    /**
     * 构建商品模型
     *
     * @param shop       店铺信息
     * @param req        请求参数
     * @return 商品模型
     */
    private ItemModel buildItemModel(ShopDTO shop, ItemAddOrUpdateRequest req) {
        ItemModel itemModel = new ItemModel();

        // 商品属性
        ItemAttributesDTO itemAttributesDTO = new ItemAttributesDTO();
        itemAttributesDTO.setOtherAttrs(req.getGroupedOtherAttributes());
        itemModel.setItemAttributes(itemAttributesDTO);

        // 商品详情图
        ItemDetailsDTO itemDetailsDTO = new ItemDetailsDTO();
        ItemDetailParam itemDetail = req.getItemDetail();
        // 保存的是 商品图片中的辅助图
        List<ImageInfo> images = itemDetail.getImages();
        if (CollUtil.isNotEmpty(images)) {
            for (ImageInfo image : images) {
                image.setUrl(imageUrlProcessor.process(image.getUrl()));
            }
        }
        itemDetailsDTO.setImagesJson(JSONUtil.toJsonStr(images));
        itemDetailsDTO.setDetail(itemDetail.getDetail());
        itemDetailsDTO.setVideoUrl(StrUtil.isNotBlank(itemDetail.getVideoUrl()) ? itemDetail.getVideoUrl() : "");
        itemDetailsDTO.setVideoThumbnailUrl(StrUtil.isNotBlank(itemDetail.getVideoThumbnailUrl()) ? itemDetail.getVideoThumbnailUrl() : "");
        // 保存的是 商品详情图片
        List<String> details = new ArrayList<>();
        if (CollUtil.isNotEmpty(itemDetail.getImageList())) {
            for (String imageUrl : itemDetail.getImageList()) {
                details.add(imageUrlProcessor.process(imageUrl));
            }
        }
        itemDetailsDTO.setDetailJson(JSONUtil.toJsonStr(details));
        itemModel.setItemDetail(itemDetailsDTO);
        // 商品基础信息
        ItemsDTO itemsDTO = BeanUtil.copyProperties(req.getItem(), ItemsDTO.class);
        itemsDTO.setStockType(ItemStockTypeEnum.SINGLE_WAREHOUSE.getCode());
        itemsDTO.setTips(0);
        if (ObjUtil.isEmpty(itemsDTO.getStatus())) {
            itemsDTO.setStatus(ItemStatusEnum.OFF_SHELF.getCode());
        }
        if (ObjUtil.isEmpty(itemsDTO.getType())) {
            itemsDTO.setType(ItemTypeEnum.NORMAL.getCode());
        }
        if (ObjUtil.isNotEmpty(itemsDTO.getBrandId())) {
            ItemBrandDTO itemBrandDTO = itemBrandGateway.selectById(itemsDTO.getBrandId());
            if (ObjUtil.isNotEmpty(itemBrandDTO)) {
                itemsDTO.setBrandName(itemBrandDTO.getName());
            }
        } else {
            // 品牌为空的情况下
            itemsDTO.setBrandId(-1L);
            itemsDTO.setBrandName("");
        }
        itemsDTO.setShopId(shop.getId());
        itemsDTO.setShopName(shop.getName());
        itemsDTO.setMainImage(imageUrlProcessor.process(req.getItem().getMainImage()));
        itemsDTO.setIndex(initItemIndex(shop.getId()));
        String itemInfoMd5 = Digestors.itemDigest(itemsDTO, itemModel.getItemDetail(), itemModel.getItemAttributes());
        itemsDTO.setItemInfoMd5(itemInfoMd5);

        Map<String, String> extra = itemsDTO.getExtra();
        if (CollUtil.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (CollUtil.isNotEmpty(req.getItemSpecificationParams())) {
            // 将规格顺序 保存到 扩展字段中
            List<Long> itemSpecIdList = req.getItemSpecificationParams().stream().map(ItemSpecificationParam::getId).collect(Collectors.toList());
            extra.put(ItemExtraIndex.specSequence.name(), JSONUtil.toJsonStr(itemSpecIdList));
        } else {
            extra.remove(ItemExtraIndex.specSequence.name());
        }
        itemsDTO.setExtraJson(JSONUtil.toJsonStr(extra));
        if (ObjUtil.isNotEmpty(req.getItem().getId())) {
            ItemsDTO oldItem = itemsGateway.getById(req.getItem().getId());
            itemsDTO.setSaleQuantity(oldItem.getSaleQuantity());
        } else {
            itemsDTO.setSaleQuantity(0);
        }
        itemModel.setItem(itemsDTO);

        // 商品运费
        ItemDeliveryFeeDTO itemDeliveryFeeDTO = new ItemDeliveryFeeDTO();
        itemDeliveryFeeDTO.setDeliveryFeeTemplateId(req.getItemDeliveryFee().getDeliveryFeeTemplateId());
        itemModel.setItemDeliveryFee(itemDeliveryFeeDTO);

        if (CollUtil.isNotEmpty(req.getItem().getShopCategoryIds())) {
            // 店铺商品小程序类目
            List<ShopCategoryItemDTO> shopCategoryItemList = req.getItem().getShopCategoryIds().stream().map(shopCategoryId -> {
                ShopCategoryItemDTO shopCategoryItemDTO = new ShopCategoryItemDTO();
                shopCategoryItemDTO.setShopId(shop.getId());
                shopCategoryItemDTO.setShopCategoryId(shopCategoryId);
                return shopCategoryItemDTO;
            }).collect(Collectors.toList());
            // 添加默认类目
            shopCategoryItemList.add(new ShopCategoryItemDTO().setShopCategoryId(0L).setShopId(shop.getId()));
            shopCategoryItemList.sort(Comparator.comparing(ShopCategoryItemDTO::getShopCategoryId));
            itemModel.setShopCategoryItemList(shopCategoryItemList);
        }

        // 商品规格信息
        itemModel.setItemsSpecificationDetailList(getItemsSpecificationDetailList(shop, req));

        // sku相关信息
        appendSkuInfoAndPrice(req, itemModel);
        return itemModel;
    }

    /**
     * 初始化商品的排序值
     */
    private int initItemIndex(Long shopId) {
        Integer smallestIndex = itemsGateway.findSmallestIndex(shopId);
        if (ObjUtil.isEmpty(smallestIndex)) {
            return 0;
        }
        return smallestIndex - 1;
    }


    private List<ItemsSpecificationDetailDTO> getItemsSpecificationDetailList(ShopDTO shop, ItemAddOrUpdateRequest req) {
        if (CollUtil.isEmpty(req.getItemSpecificationParams())) {
            return Collections.emptyList();
        }
        List<ItemsSpecificationDetailDTO> itemsSpecificationDetailList = new ArrayList<>();
        for (ItemSpecificationParam itemSpecificationParam : req.getItemSpecificationParams()) {
            Long id = itemSpecificationParam.getId();
            List<ItemSpecificationDetailParam> itemSpecificationDetailList = itemSpecificationParam.getItemSpecificationDetailParams().stream().filter(itemSpecificationDetailParam -> StrUtil.isNotBlank(itemSpecificationDetailParam.getName())).collect(Collectors.toList());
            for (int i = 0; i < itemSpecificationDetailList.size(); i++) {
                ItemSpecificationDetailParam itemSpecificationDetailParam = itemSpecificationDetailList.get(i);
                ItemsSpecificationDetailDTO itemsSpecificationDetailDTO = new ItemsSpecificationDetailDTO();
                itemsSpecificationDetailDTO.setShopId(shop.getId());
                itemsSpecificationDetailDTO.setItemSpecificationId(id);
                itemsSpecificationDetailDTO.setName(itemSpecificationDetailParam.getName());
                itemsSpecificationDetailDTO.setSequence(i);
                itemsSpecificationDetailDTO.setFrontId(itemSpecificationDetailParam.getFrontId());
                itemsSpecificationDetailDTO.setVersion(0);
                itemsSpecificationDetailList.add(itemsSpecificationDetailDTO);
            }
        }
        return itemsSpecificationDetailList;
    }

    private void appendSkuInfoAndPrice(ItemAddOrUpdateRequest req, ItemModel itemModel) {
        List<SkuWithCustomParam> skuWithCustomList = req.getSkuWithCustoms();
        List<SkuRelatedInfoDto> skuRelatedInfoList = new ArrayList<>();
        List<Integer> priceList = new ArrayList<>();
        Integer lowerPrice;
        Integer highPrice;
        Integer itemStockQuantity = 0;
        Map<String, ItemsSpecificationDetailDTO> frontIdByMap = itemModel.getItemsSpecificationDetailList().stream().collect(Collectors.toMap(ItemsSpecificationDetailDTO::getFrontId, Function.identity()));
        int skuSize = skuWithCustomList.size();
        for (SkuWithCustomParam skuWithCustomParam : skuWithCustomList) {
            SkuRelatedInfoDto skuRelatedInfoDto = new SkuRelatedInfoDto();
            skuRelatedInfoDto.setDeleteActivityCommissionConfig(req.getDeleteActivityCommissionConfig());
            if (req.getItem().getIsBonded() == ItemTradeTypeEnum.BONDED.getCode()) {
                // 保税的商品 才需要设置海关信息
                SkuCustomParam skuCustom = skuWithCustomParam.getSkuCustom();
                SkuCustomDTO skuCustomDTO = BeanUtil.copyProperties(skuCustom, SkuCustomDTO.class);
                skuRelatedInfoDto.setSkuCustom(skuCustomDTO);
            }
            SkuParam sku = skuWithCustomParam.getSku();
            itemStockQuantity += sku.getStockQuantity();
            priceList.add(sku.getPrice());
            SkusDTO skusDTO = BeanUtil.copyProperties(sku, SkusDTO.class);
            if (StrUtil.isNotBlank(sku.getSkuId())) {
                if (!sku.getSkuId().contains("-")) {
                    skusDTO.setId(Long.parseLong(sku.getSkuId()));
                }
            }
            skusDTO.setShopId(itemModel.getItem().getShopId());
            if (ObjUtil.isEmpty(skusDTO.getStatus())) {
                // 默认下架
                skusDTO.setStatus(SkuStatusEnum.OFF_SHELF.getCode());
            }
            // 单商品
            if (skuSize == 1) {
                skusDTO.setStatus(itemModel.getItem().getStatus());
            }
            // 组合品状态跟随 item 走
            if (req.getItem().getType().equals(ItemTypeEnum.BUNDLE.getCode())) {
                skusDTO.setStatus(itemModel.getItem().getStatus());
            }
            skusDTO.setType(req.getItem().getIsBonded());
            skusDTO.setStockType(SkuStockTypeEnum.SINGLE_WAREHOUSE.getCode());
            skusDTO.setInitialStockQuantity(sku.getStockQuantity());
            skusDTO.setExtra(JSONUtil.toJsonStr(sku.getExtraMap()));

            // 获取商品额外配置
			List<IntermediateInfoDTO> intermediateInfoList = getIntermediateInfoDTO(req, itemModel, sku);
			skuRelatedInfoDto.setIntermediateInfoList(intermediateInfoList);
            List<String> specDetails = sku.getSpecDetails();
            if (CollUtil.isNotEmpty(specDetails)) {
                List<SkusSpecificationRelevanceDTO> skusSpecificationRelevanceList = new ArrayList<>();
                for (int i = 0; i < specDetails.size(); i++) {
                    String specDetail = specDetails.get(i);
                    SkusSpecificationRelevanceDTO skusSpecificationRelevanceDTO = new SkusSpecificationRelevanceDTO();
                    skusSpecificationRelevanceDTO.setShopId(itemModel.getItem().getShopId());
                    skusSpecificationRelevanceDTO.setSpecDetailFrontId(specDetail);
                    String specName = frontIdByMap.get(specDetail).getName();
                    skusSpecificationRelevanceDTO.setSpecName(specName);
                    skusSpecificationRelevanceDTO.setSequence(i);
                    skusSpecificationRelevanceList.add(skusSpecificationRelevanceDTO);
                }
                skuRelatedInfoDto.setSkusSpecificationRelevanceList(skusSpecificationRelevanceList);
                String specName = skusSpecificationRelevanceList.stream().map(SkusSpecificationRelevanceDTO::getSpecName).collect(Collectors.joining("/"));
                skusDTO.setSpecification(specName);
            }
            skuRelatedInfoDto.setSku(skusDTO);

            // 组合商品关联
            if (itemModel.getItem().getType().equals(ItemTypeEnum.BUNDLE.getCode())) {
                skuRelatedInfoDto.setSkuComboRelationList(BeanUtil.copyToList(sku.getSkuComboList(), SkuComboRelationDTO.class, CopyOptions.create().setIgnoreProperties("skuId")));
            }
            skuRelatedInfoList.add(skuRelatedInfoDto);
        }
        priceList.sort(Integer::compareTo);
        lowerPrice = priceList.get(0);
        highPrice = priceList.get(priceList.size() - 1);
        itemModel.getItem().setLowPrice(lowerPrice);
        itemModel.getItem().setHighPrice(highPrice);
        itemModel.getItem().setStockQuantity(itemStockQuantity);
        itemModel.setSkuRelatedInfoList(skuRelatedInfoList);
    }


	private List<IntermediateInfoDTO> getIntermediateInfoDTO(ItemAddOrUpdateRequest req, ItemModel itemModel, SkuParam sku) {
		List<IntermediateInfoDTO> intermediateInfoList = new ArrayList<>();
		// 商品类型 （1.普通商品，2，组合商品）
        Integer itemType = itemModel.getItem().getType();
		// 是否可用福豆
		boolean whetherUseCashGift = ObjUtil.isNotEmpty(req.getIsCashGift()) && req.getIsCashGift() == 1;
		boolean whetherCommission = ObjUtil.isNotEmpty(req.getIsCommission()) && req.getIsCommission() == 1;
		boolean whetherComboUseCashGift = ObjUtil.isNotEmpty(sku.getComboCashGiftSwitch()) && sku.getComboCashGiftSwitch() == 1;

		if (whetherUseCashGift || whetherCommission || whetherComboUseCashGift) {
			// 开启福豆 或者 开启单品佣金
			IntermediateInfoDTO normalIntermediateInfo = new IntermediateInfoDTO();
			normalIntermediateInfo.setType(ThirdIntermediateType.SKU.value());
			normalIntermediateInfo.setIsCashGift(0);
			normalIntermediateInfo.setVersion(3);
			normalIntermediateInfo.setStatus(1);
			Map<String, String> extra = new HashMap<>();
			extra.putIfAbsent("VERSION", "3");
			normalIntermediateInfo.setExtra(extra);
			normalIntermediateInfo.setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode());
			if (whetherUseCashGift || whetherComboUseCashGift) {
				// 开启福豆
				if (itemType.equals(ItemTypeEnum.NORMAL.getCode())) {
					normalIntermediateInfo.setIsCashGift(req.getIsCashGift());
					// 开启福豆
					normalIntermediateInfo.setMaxDeduction(sku.getMaxDeduction());
					normalIntermediateInfo.setUnit(sku.getCashGiftUnit());
					// 设置当日的开始时间戳
					if (ObjUtil.isNotEmpty(req.getCashGiftUseTimeStart()) && ObjUtil.isNotEmpty(req.getCashGiftUseTimeEnd())) {
						normalIntermediateInfo.setCashGiftUseTimeStart(DateUtil.beginOfDay(new Date(req.getCashGiftUseTimeStart())).getTime());
						normalIntermediateInfo.setCashGiftUseTimeEnd(DateUtil.endOfDay(new Date(req.getCashGiftUseTimeEnd())).getTime());
					}
				} else if (itemType.equals(ItemTypeEnum.BUNDLE.getCode())) {
					// 组合品
					normalIntermediateInfo.setIsCashGift(sku.getComboCashGiftSwitch());
					// 开启组合品的福豆
					normalIntermediateInfo.setMaxDeduction(sku.getComboCashGiftMaxDeduction());
					normalIntermediateInfo.setUnit(sku.getComboCashGiftUnit());
					if (ObjUtil.isNotEmpty(sku.getComboCashGiftUseTimeStart()) && ObjUtil.isNotEmpty(sku.getComboCashGiftUseTimeEnd())) {
						// 设置当日的开始时间戳
						normalIntermediateInfo.setCashGiftUseTimeStart(DateUtil.beginOfDay(new Date(sku.getComboCashGiftUseTimeStart())).getTime());
						normalIntermediateInfo.setCashGiftUseTimeEnd(DateUtil.endOfDay(new Date(sku.getComboCashGiftUseTimeEnd())).getTime());
					}
                }
				intermediateInfoList.add(normalIntermediateInfo);
            }
			if (whetherCommission) {
				// 开启单品佣金
				normalIntermediateInfo.setIsCommission(req.getIsCommission());
				normalIntermediateInfo.setServiceProviderRate(req.getServiceProviderRate());
				normalIntermediateInfo.setServiceProviderFee(req.getServiceProviderFee());
				normalIntermediateInfo.setFirstRate(req.getFirstRate());
				normalIntermediateInfo.setFirstFee(req.getFirstFee());
				normalIntermediateInfo.setSecondFee(req.getSecondFee());
				normalIntermediateInfo.setSecondRate(req.getSecondRate());
				intermediateInfoList.add(normalIntermediateInfo);
			}
        }
		SkuCommissionConfigParam skuCommissionConfigParam = req.getActivityCommissionConfig();
		if (ObjUtil.isNotEmpty(skuCommissionConfigParam)) {
			IntermediateInfoDTO activityIntermediateInfoDTO = new IntermediateInfoDTO();
			activityIntermediateInfoDTO.setType(ThirdIntermediateType.SKU.value());
			activityIntermediateInfoDTO.setIsCashGift(0);
			activityIntermediateInfoDTO.setVersion(3);
			activityIntermediateInfoDTO.setStatus(1);
			Map<String, String> extra = new HashMap<>();
			extra.putIfAbsent("VERSION", "3");
			activityIntermediateInfoDTO.setExtra(extra);
			activityIntermediateInfoDTO.setMatchingType(IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode());
			activityIntermediateInfoDTO.setMatchingStartTime(DateUtil.parseDateTime(skuCommissionConfigParam.getMatchingStartTimeString()));
			activityIntermediateInfoDTO.setMatchingEndTime(DateUtil.parseDateTime(skuCommissionConfigParam.getMatchingEndTimeString()));
			activityIntermediateInfoDTO.setServiceProviderFee(skuCommissionConfigParam.getServiceProviderFee());
			activityIntermediateInfoDTO.setServiceProviderRate(skuCommissionConfigParam.getServiceProviderRate());
            activityIntermediateInfoDTO.setFirstFee(skuCommissionConfigParam.getFirstFee());
            activityIntermediateInfoDTO.setFirstRate(skuCommissionConfigParam.getFirstRate());
            activityIntermediateInfoDTO.setSecondFee(skuCommissionConfigParam.getSecondFee());
            activityIntermediateInfoDTO.setSecondRate(skuCommissionConfigParam.getSecondRate());
			activityIntermediateInfoDTO.setIsCommission(1);
			intermediateInfoList.add(activityIntermediateInfoDTO);
		}
		return intermediateInfoList;
    }

    private void autoFillCommonParam(ItemAddOrUpdateRequest req) {
        for (SkuWithCustomParam skuWithCustom : req.getSkuWithCustoms()) {
            SkuParam sku = skuWithCustom.getSku();
            if (CollUtil.isEmpty(sku.getTags())) {
                sku.setTags(new HashMap<>());
            }
            sku.getTags().putIfAbsent("pushSystem", "3");
            if (ObjUtil.isEmpty(sku.getVersion())) {
                sku.setVersion(0);
            }
        }
    }

    private void validateCreateItemRequest(ItemAddOrUpdateRequest req, ShopDTO shopDTO) {
        Long shopId = shopDTO.getId();
        if (ObjUtil.isEmpty(shopDTO)) {
            throw new RuntimeException("未查到对应的店铺信息");
        }
        ItemParam item = req.getItem();
        if (ObjUtil.isEmpty(item.getIsThirdPartyItem())) {
            throw new RuntimeException("商品标识不能为空");
        }
        if (!ThirdPartyItemType.getAllType().contains(item.getIsThirdPartyItem())) {
            throw new RuntimeException("商品标识值有误");
        }
        if (item.getIsThirdPartyItem() == ThirdPartyItemType.THIRD_PARTY_ITEM.getType()) {
            // 第三方平台同步过来的商品相关校验
            if (functionSwitch.getThirdPartyStockLimit()) {
                validateThirdPartyStock(shopId, req.getSkuWithCustoms());
            }
        }
        // 校验运费模板
        validateDeliveryFee(shopId, req.getItemDeliveryFee());
        // 校验可售地区模板
        validateRestrictedSalesAreaTemplate(shopId, item.getRestrictedSalesAreaTemplateId());
        if (ObjUtil.isNotEmpty(req.getIsCashGift()) && req.getIsCashGift() == 1) {
            // 校验福豆相关参数
            validateCashGift(req.getSkuWithCustoms());
        }
        if (req.getItem().getType() == ItemTypeEnum.BUNDLE.getCode()) {
            // 组合品校验
            validateCombo(req.getSkuWithCustoms());
        }
        if (req.getSkuWithCustoms().size() > 125) {
            throw new RuntimeException("规格数量不能超过125个");
        }
        validateSkuInfo(req.getSkuWithCustoms(), req.getItem());
    }

    private void validateCashGift(List<SkuWithCustomParam> skuWithCustoms) {
        for (SkuWithCustomParam skuWithCustom : skuWithCustoms) {
            SkuParam sku = skuWithCustom.getSku();
            if (ObjUtil.isEmpty(sku.getMaxDeduction())) {
                throw new RuntimeException("商品可使用福豆最大抵扣金额/比例不能为空");
            }
            if (ObjUtil.isEmpty(sku.getCashGiftUnit())) {
                throw new RuntimeException("商品可使用福豆抵扣单位不能为空");
            }
            if (sku.getCashGiftUnit().equals(CashGiftUnitEnum.YUAN.getCode())) {
                // 元
                BigDecimal maxDeduction = sku.getMaxDeduction().multiply(BigDecimal.valueOf(100));
                maxDeduction = maxDeduction.multiply(beanConvertRate);//福豆按照比例转换成金额
                if (maxDeduction.intValue() >= sku.getPrice()) {
                    throw new RuntimeException("商品可使用福豆最大抵扣金额/比例不能大于等于商品价格");
                }
            } else if (sku.getCashGiftUnit().equals(CashGiftUnitEnum.PERCENT.getCode())) {
                if (sku.getMaxDeduction().compareTo(BigDecimal.valueOf(100)) >= 0) {
                    throw new RuntimeException("商品可使用福豆最大抵扣金额/比例不能大于等于100");
                }
            } else {
                throw new RuntimeException("商品可使用福豆抵扣单位有误");
            }
        }
    }

    /**
     * 校验可售地区
     *
     * @param shopId                        店铺id
     * @param restrictedSalesAreaTemplateId 可售地区模板id
     */
    private void validateRestrictedSalesAreaTemplate(Long shopId, Long restrictedSalesAreaTemplateId) {
        if (ObjUtil.isEmpty(restrictedSalesAreaTemplateId)) {
            throw new RuntimeException("可售地区不能为空");
        }
        RestrictedSalesAreaTemplateDTO restrictedSalesAreaTemplate = restrictedSalesAreaTemplateGateway.selectOne(new RestrictedSalesAreaTemplateQry().setShopId(shopId).setId(restrictedSalesAreaTemplateId));
        if (ObjUtil.isEmpty(restrictedSalesAreaTemplate)) {
            throw new RuntimeException("可售地区模板不存在");
        }
        if (restrictedSalesAreaTemplate.getStatus().equals(RestrictedSalesAreaTemplateStatusEnum.INACTIVE.getCode())) {
            throw new RuntimeException("可售地区模板已停用");
        }
    }

    /**
     * 校验运费模板
     *
     * @param shopId          店铺id
     * @param itemDeliveryFee 运费模板
     */
    private void validateDeliveryFee(Long shopId, ItemDeliveryFeeParam itemDeliveryFee) {
        if (ObjUtil.isEmpty(itemDeliveryFee)) {
            throw new RuntimeException("运费模板不能为空");
        }
        Long deliveryFeeTemplateId = itemDeliveryFee.getDeliveryFeeTemplateId();
        if (ObjUtil.isEmpty(deliveryFeeTemplateId)) {
            throw new RuntimeException("运费模板id不能为空");
        }
        DeliveryFeeTemplateDTO deliveryFeeTemplate = deliveryFeeTemplateGateway.findDeliveryFeeTemplateById(shopId, deliveryFeeTemplateId);
        if (ObjUtil.isEmpty(deliveryFeeTemplate)) {
            throw new RuntimeException("运费模板不存在");
        }
    }

    private void validateThirdPartyStock(Long shopId, List<SkuWithCustomParam> skuWithCustoms) {
        for (SkuWithCustomParam skuWithCustom : skuWithCustoms) {
            SkuParam sku = skuWithCustom.getSku();
            if (StrUtil.isNotBlank(sku.getOuterSkuId())) {
                throw new RuntimeException("第三方平台商品SKU编号不能为空");
            }
            Map<String, String> tags = sku.getTags();
            String pushSystemStr = tags.get("pushSystem");
            List<Integer> pushSystems = Splitter.on(',')
                    .trimResults()  // 去除每个元素前后的空格
                    .omitEmptyStrings()  // 忽略空字符串
                    .splitToList(pushSystemStr).stream().filter(StrUtil::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
            int totalAuthenticStock = 0;
            for (Integer pushSystem : pushSystems) {
                List<ThirdPartySkuStockDTO> thirdPartySkuStockList = thirdPartySkuStockGateway.findByThirdPartyIdAndOuterSkuId(shopId, pushSystem, sku.getOuterSkuId());
                if (CollUtil.isNotEmpty(thirdPartySkuStockList)) {
                    for (ThirdPartySkuStockDTO thirdPartySkuStockDTO : thirdPartySkuStockList) {
                        totalAuthenticStock += thirdPartySkuStockDTO.getAuthenticStock();
                    }
                }
            }
            if (sku.getStockQuantity() > totalAuthenticStock) {
                throw new RuntimeException("平台可售库存不能大于仓库正品库存");
            }
        }
    }
}