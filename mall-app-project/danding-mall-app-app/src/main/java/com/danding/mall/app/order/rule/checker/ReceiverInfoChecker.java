package com.danding.mall.app.order.rule.checker;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.danding.mall.app.domain.items.gateway.IRestrictedSalesAreaTemplateGateway;
import com.danding.mall.app.items.dto.data.ItemsDTO;
import com.danding.mall.app.items.dto.data.RestrictedSalesAreaTemplateDTO;
import com.danding.mall.app.order.api.OrderChecker;
import com.danding.mall.app.order.enums.OrderSourceEnum;
import com.danding.mall.app.order.vo.RichOrder;
import com.danding.mall.app.order.vo.RichSku;
import com.danding.mall.app.order.vo.RichSkusByShop;
import com.danding.mall.app.receiver.dto.data.ReceiverInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Mail: <EMAIL>
 * Data: 16/7/26
 * Author: yangzefeng
 */
@Slf4j
@Component
public class ReceiverInfoChecker implements OrderChecker {


    @Resource
    private IRestrictedSalesAreaTemplateGateway restrictedSalesAreaTemplateGateway;

    private static final String PROVINCE_SUFFIX = "省";
    private static final String CITY_SUFFIX = "市";

    public ReceiverInfoChecker(){}

    public ReceiverInfoChecker(IRestrictedSalesAreaTemplateGateway restrictedSalesAreaTemplateGateway) {
        this.restrictedSalesAreaTemplateGateway = restrictedSalesAreaTemplateGateway;
    }

    @Override
    public void canBuy(OrderSourceEnum orderSource, RichOrder richOrder) {
        ReceiverInfoDTO receiver = richOrder.getReceiverInfo();
        if (receiver == null) {
            throw new RuntimeException("收货人信息为空");
        }

        String province = receiver.getProvince();
        for (RichSkusByShop richSkuByShop : richOrder.getRichSkusByShops()) {
            for (RichSku richSku : richSkuByShop.getRichSkus()) {
                if (!allow(richSku.getItem(), province)) {
                    throw new RuntimeException(String.format("商品不支持在%s售卖，请重新选择收货地址", province));
                }
            }
        }
    }

    private boolean allow(ItemsDTO item, String province) {
        if (item == null || item.getRestrictedSalesAreaTemplateId() == null) {
            return true;
        }

        RestrictedSalesAreaTemplateDTO template = restrictedSalesAreaTemplateGateway.selectById(item.getRestrictedSalesAreaTemplateId());
        if (template == null) {
            return true;
        }
        if (Objects.equals(1, template.getIsValid()) && Objects.equals(1, template.getStatus())) {
            String configure = template.getAllowProvince();
            if (StringUtils.isBlank(configure)) {
                return false;
            }
            List<String> list = JSON.parseArray(configure, String.class);
            if (CollUtil.isEmpty(list)) {
                return true;
            }
            return list.contains(province) || list.contains(province + PROVINCE_SUFFIX) || list.contains(province + CITY_SUFFIX);
        }
        return true;
    }
}
