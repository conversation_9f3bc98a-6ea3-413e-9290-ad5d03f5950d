package com.danding.mall.app.sensitive;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.core.client.PageDTO;
import com.danding.core.tool.utils.BeanUtil;
import com.danding.mall.app.base.constants.TopicConstants;
import com.danding.mall.app.base.session.helper.ShopHelper;
import com.danding.mall.app.sensitive.dto.SensitiveWordQry;
import com.danding.mall.app.sensitive.dto.data.SensitiveWordDTO;
import com.danding.mall.app.sensitive.dto.data.SensitiveWordImportDTO;
import com.danding.mall.app.sensitive.message.SensitiveWordMessage;
import com.danding.mall.app.sensitive.vo.request.SensitiveWordQryRequest;
import com.danding.mall.app.sensitive.vo.request.SensitiveWordRequest;
import com.danding.mall.app.sensitive.vo.response.SensitiveWordResponse;
import com.danding.mall.app.sensitive.api.ISensitiveWordService;
import com.danding.mall.app.domain.sensitive.gateway.ISensitiveWordGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2025-03-25
*/
@Slf4j
@Service
public class SensitiveWordServiceImpl implements ISensitiveWordService {

    @Resource
    private ISensitiveWordGateway sensitiveWordGateway;
    @Resource
    private SpringRocketMQProducer springRocketMQProducer;
    @Resource
    private SensitiveFilter sensitiveFilter;

    @Override
    public PageDTO<SensitiveWordResponse> queryListPage(SensitiveWordQryRequest request) {
        if (StringUtils.isNotEmpty(request.getRemark())) {
            Assert.isTrue(request.getRemark().length() <= 16, "备注搜索字数长度不能超过16个");
        }
        if (CollectionUtil.isNotEmpty(request.getWordList())) {
            for (String w : request.getWordList()) {
                Assert.isTrue(w.length() <= 6, "敏感词搜索字数长度不能超过6个");
            }
        }

        Long shopId = ShopHelper.getId();
        if (Objects.nonNull(shopId) && !Objects.equals(0L, shopId)) {
            request.setShopId(shopId);
        }

        SensitiveWordQry qry = new SensitiveWordQry();
        qry.setShopId(request.getShopId());
        qry.setWord(request.getWord());
        qry.setRemarkLike(request.getRemarkLike());

        PageDTO<SensitiveWordDTO> page = sensitiveWordGateway.page(qry, request);

        List<SensitiveWordResponse> responseList = new ArrayList<>();
        for (SensitiveWordDTO sensitiveWordDTO : page.getDataList()) {
            responseList.add(BeanUtil.copy(sensitiveWordDTO, SensitiveWordResponse.class));
        }
        PageDTO<SensitiveWordResponse> pageDTO = new PageDTO<>();
        return pageDTO.vo(responseList, request, page.getPage().getTotalCount());
    }

    @Override
    public Boolean saveOrEdt(SensitiveWordRequest request) {
        Assert.isTrue(StringUtils.isNotEmpty(request.getWord()), "敏感词不能为空");
        if (Objects.nonNull(request.getId())) {
            SensitiveWordDTO sensitiveWordDTO = sensitiveWordGateway.selectById(request.getId());
            Assert.notNull(sensitiveWordDTO, "编辑敏感词不存在");
            String oldWord = sensitiveWordDTO.getWord();
            sensitiveWordDTO.setWord(request.getWord());
            sensitiveWordDTO.setRemark(request.getRemark());
            boolean update = sensitiveWordGateway.updateById(request.getId(), sensitiveWordDTO);
            if (update && !Objects.equals(request.getWord(), oldWord)) {
                SensitiveWordMessage wordMessage = new SensitiveWordMessage();
                wordMessage.setShopId(sensitiveWordDTO.getShopId());
                wordMessage.setType(3);
                wordMessage.setWord(request.getWord());
                wordMessage.setOldWord(oldWord);
                SendResult result = springRocketMQProducer.syncSend(TopicConstants.SENSITIVE_WORD_TOPIC, new SpringMessage<>(wordMessage));
                if(SendStatus.SEND_OK.equals(result.getSendStatus())){
                    log.info("敏感词修改 消息发送成功 {}", JSONObject.toJSONString(wordMessage));
                }
                sensitiveFilter.removeKeyword(wordMessage.getOldWord(), wordMessage.getShopId());
                sensitiveFilter.addKeyword(wordMessage.getWord(), wordMessage.getShopId());
            }
            return update;
        } else {
            SensitiveWordDTO sensitiveWordDTO = new SensitiveWordDTO();
            sensitiveWordDTO.setWord(request.getWord());
            sensitiveWordDTO.setRemark(request.getRemark());
            sensitiveWordDTO.setShopId(request.getShopId());
            sensitiveWordDTO.setStatusTime(0L);
            Long shopId = ShopHelper.getId();
            if (Objects.nonNull(shopId) && !Objects.equals(0L, shopId)) {
                sensitiveWordDTO.setShopId(shopId);
            }
            try {
                boolean insert = sensitiveWordGateway.insert(sensitiveWordDTO);
                if (insert) {
                    SensitiveWordMessage wordMessage = new SensitiveWordMessage();
                    wordMessage.setShopId(sensitiveWordDTO.getShopId());
                    wordMessage.setType(1);
                    wordMessage.setWord(sensitiveWordDTO.getWord());
                    SendResult result = springRocketMQProducer.syncSend(TopicConstants.SENSITIVE_WORD_TOPIC, new SpringMessage<>(wordMessage));
                    if(SendStatus.SEND_OK.equals(result.getSendStatus())){
                        log.info("敏感词新增 消息发送成功 {}", JSONObject.toJSONString(wordMessage));
                    }
                    sensitiveFilter.addKeyword(wordMessage.getWord(), wordMessage.getShopId());
                }
                return insert;
            } catch (DuplicateKeyException e) {
                throw new RuntimeException("已存在相同的敏感词");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteBatch(SensitiveWordRequest request) {
        Assert.isTrue(CollectionUtil.isNotEmpty(request.getIdList()), "idList不能为空");

        SensitiveWordQry qry = new SensitiveWordQry();
        qry.setIdList(request.getIdList());
        List<SensitiveWordDTO> wordDTOList = sensitiveWordGateway.selectList(qry);
        if (CollectionUtil.isEmpty(wordDTOList) || wordDTOList.size() != request.getIdList().size()) {
            throw new RuntimeException("删除敏感词失败");
        }
        List<SensitiveWordDTO> collect = wordDTOList.stream().map(wordDTO -> {
            wordDTO.setStatusTime(System.currentTimeMillis());
            return wordDTO;
        }).collect(Collectors.toList());
        boolean updateBatch = sensitiveWordGateway.updateBatch(collect);
        if (updateBatch) {
            boolean delete = sensitiveWordGateway.delete(request.getIdList());
            if (!delete) {
                throw new RuntimeException("删除敏感词失败");
            }
            for (SensitiveWordDTO sensitiveWordDTO : collect) {
                SensitiveWordMessage wordMessage = new SensitiveWordMessage();
                wordMessage.setShopId(sensitiveWordDTO.getShopId());
                wordMessage.setType(2);
                wordMessage.setWord(sensitiveWordDTO.getWord());
                SendResult result = springRocketMQProducer.syncSend(TopicConstants.SENSITIVE_WORD_TOPIC, new SpringMessage<>(wordMessage));
                if(SendStatus.SEND_OK.equals(result.getSendStatus())){
                    log.info("敏感词删除 消息发送成功 {}", JSONObject.toJSONString(wordMessage));
                }
                sensitiveFilter.removeKeyword(wordMessage.getWord(), wordMessage.getShopId());
            }
        }
        return updateBatch;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatch(List<SensitiveWordImportDTO> wordDTOList) {
        if (CollectionUtil.isEmpty(wordDTOList)) {
            return Boolean.FALSE;
        }
        List<SensitiveWordDTO> collect = wordDTOList.stream().map(importDTO -> {
            SensitiveWordDTO wordDTO = new SensitiveWordDTO();
            wordDTO.setWord(importDTO.getWord());
            wordDTO.setRemark(importDTO.getRemark());
            wordDTO.setShopId(ShopHelper.getId());
            wordDTO.setStatusTime(0L);
            return wordDTO;
        }).collect(Collectors.toList());

        boolean insertBatch = sensitiveWordGateway.insertBatch(collect);
        if (!insertBatch) {
            throw new RuntimeException("导入敏感词失败");
        }
        for (SensitiveWordDTO sensitiveWordDTO : collect) {
            SensitiveWordMessage wordMessage = new SensitiveWordMessage();
            wordMessage.setShopId(sensitiveWordDTO.getShopId());
            wordMessage.setType(1);
            wordMessage.setWord(sensitiveWordDTO.getWord());
            SendResult result = springRocketMQProducer.syncSend(TopicConstants.SENSITIVE_WORD_TOPIC, new SpringMessage<>(wordMessage));
            if(SendStatus.SEND_OK.equals(result.getSendStatus())){
                log.info("敏感词导入 消息发送成功 {}", JSONObject.toJSONString(wordMessage));
            }
            sensitiveFilter.addKeyword(wordMessage.getWord(), wordMessage.getShopId());
        }
        return Boolean.TRUE;
    }

}