package com.danding.mall.app.userAmount;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.mall.app.domain.userAmount.gateway.IOrderDeductionAmountGateway;
import com.danding.mall.app.userAmount.api.IOrderDeductionAmountService;
import com.danding.mall.app.userAmount.api.IUserAmountSendService;
import com.danding.mall.app.userAmount.api.IUserAmountService;
import com.danding.mall.app.userAmount.dto.OrderDeductionAmountQry;
import com.danding.mall.app.userAmount.dto.UserAmountSendQry;
import com.danding.mall.app.userAmount.dto.data.OrderDeductionAmountDTO;
import com.danding.mall.app.userAmount.dto.data.OrderInfoDTO;
import com.danding.mall.app.userAmount.dto.data.UserAmountSendDTO;
import com.danding.mall.app.userAmount.enums.UserAmountSendTypeEnum;
import com.danding.mall.app.userAmount.enums.UserAmountTypeEnum;
import com.danding.mall.app.userAmount.vo.OrderInfoVO;
import com.danding.mall.app.userAmount.vo.UserAmountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 礼/福卡订单扣减记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Slf4j
@Service
@RefreshScope
public class OrderDeductionAmountServiceImpl implements IOrderDeductionAmountService {
    private static final String PARANA_ORDER_AMOUNT_LOCK_KEY = "PARANA_ORDER_AMOUNT_LOCK_KEY";//用于福豆：扣减、回退、过期
    @Value("${minimumOrderAmount:5}")
    private BigDecimal minimumOrderAmount;
    @Value("${minimumItemAmount:0.1}")
    private BigDecimal minimumItemAmount;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IOrderDeductionAmountGateway orderDeductionAmountGateway;
    @Resource
    private IUserAmountService userAmountService;
    @Resource
    private IUserAmountSendService userAmountSendService;

    /**
     * 商品优惠推荐
     *
     * @return orderInfoVOList
     * @Param OrderInfoDTO itemId 商品id
     * @Param OrderInfoDTO amount 商品金额（可能是优惠券后的金额）
     * @Param OrderInfoDTO maxDiscountAmount 商品最高优惠金额
     * @Param shopId 店铺id
     * @Param userId 用户id
     * @Param amountType 金额类型 1福豆，2福卡
     * @Param tradeType 贸易类型 1保税，2大贸
     */
    public List<OrderInfoVO> recommendDiscountsForProduct(List<OrderInfoDTO> orderInfoDTOList, Long shopId, Long userId, Integer amountType, Integer tradeType) {
        if (CollectionUtils.isEmpty(orderInfoDTOList)) {
            throw new RuntimeException("用户ID：" + userId + " 店铺id:" + shopId + "订单信息不能为空！");
        }
        List<OrderInfoVO> orderInfoVOList = new ArrayList<>();

        BigDecimal orderAmount = orderInfoDTOList.stream().map(OrderInfoDTO::getAmount)  // 确保金额不为 null
                .filter(Objects::nonNull)  // 提取 amount 字段
                .reduce(BigDecimal.ZERO, BigDecimal::add);  // 求和，初始值为 0

        //贸易类型保税，金额类型为福豆
        if (amountType.equals(UserAmountTypeEnum.BALANCE.getCode())) {
            orderInfoVOList = recommendDiscountsForGift(orderInfoDTOList, orderAmount, userId, shopId, tradeType);
        }

        //贸易类型保税，金额类型为福卡
        if (amountType.equals(UserAmountTypeEnum.CASH.getCode())) {
            orderInfoVOList = recommendDiscountsForCash(orderInfoDTOList, orderAmount, userId, shopId, tradeType);
        }
        return orderInfoVOList;
    }

    /**
     * 福豆优惠推荐
     *
     * @return orderInfoVOList
     * @Param orderInfoDTOList 订单信息
     */
    public List<OrderInfoVO> recommendDiscountsForGift(List<OrderInfoDTO> orderInfoDTOList, BigDecimal orderAmount, Long userId, Long shopId, Integer tradeType) {
            List<OrderInfoVO> orderInfoVOList = new ArrayList<>();
        try {
            log.info("福豆优惠推荐 用户ID： {} 店铺id: {} 订单金额：{} 订单信息：{}", userId, shopId, orderAmount, JSON.toJSONString(orderInfoDTOList));
            UserAmountVO userAmountVO = this.userAmountService.detailAmount(userId, shopId);
            BigDecimal giftAmount = new BigDecimal(0);
            if (userAmountVO != null && userAmountVO.getGiftAmount() != null && userAmountVO.getGiftAmount().compareTo(BigDecimal.ZERO) > 0) {
                giftAmount = userAmountVO.getGiftAmount();
            }
            //如果没有福豆，则不参与优惠
            if (giftAmount.compareTo(BigDecimal.ZERO) <= 0) {
                orderInfoVOList = BeanUtil.copyToList(orderInfoDTOList, OrderInfoVO.class);
                //设置orderInfoVOList的discountAmount为0
                orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
                return orderInfoVOList;
            }
            List<OrderInfoDTO> orderInfoDTOListForDiscountZero = new ArrayList<>();
            List<OrderInfoDTO> orderInfoDTOListForDiscount = new ArrayList<>();
            if (tradeType == 1) {//保税才需要订单不能小于minimumOrderAmount，单品不能小于minimumItemAmount
                if (orderAmount.compareTo(minimumOrderAmount) < 0) {//订单金额小于5
                    //设置orderInfoVOList的discountAmount为0
                    orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
                    log.error("用户ID：" + userId + " 店铺id:" + shopId + "订单金额必须大于：" + minimumOrderAmount);
                    return orderInfoVOList;
                }
                //订单金额减去订单保底金额
                orderAmount = orderAmount.subtract(minimumOrderAmount);
                //未参与优惠的品集合 商品最高优惠金额为0 或者 amount<=0.1的集合
                orderInfoDTOListForDiscountZero = orderInfoDTOList.stream().filter(orderInfoDTO -> (orderInfoDTO.getMaxDiscountAmount() == null || orderInfoDTO.getMaxDiscountAmount().compareTo(BigDecimal.ZERO) == 0) || (orderInfoDTO.getAmount() == null || (orderInfoDTO.getAmount().divide(orderInfoDTO.getSkuNum(), 2, RoundingMode.DOWN)).compareTo(minimumItemAmount) <= 0)).collect(Collectors.toList());
                if (isDiscountAmountZero(orderInfoDTOListForDiscountZero)) { //校验amount<0.1的商品
                    orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
                    log.error("用户ID：" + userId + " 店铺id:" + shopId + "订单中存在金额小于0.1的商品，无法参与优惠！");
                    return orderInfoVOList;
                }
                //参与优惠的商品集合 maxDiscountAmount>0的商品且amount>0.1
                orderInfoDTOListForDiscount = orderInfoDTOList.stream().filter(orderInfoDTO -> orderInfoDTO.getMaxDiscountAmount() != null && orderInfoDTO.getMaxDiscountAmount().compareTo(BigDecimal.ZERO) > 0 && orderInfoDTO.getAmount() != null && orderInfoDTO.getAmount().compareTo(minimumItemAmount) > 0).collect(Collectors.toList());
            } else {
                //大贸
                orderInfoDTOListForDiscountZero = orderInfoDTOList.stream().filter(orderInfoDTO -> (orderInfoDTO.getMaxDiscountAmount() == null || orderInfoDTO.getMaxDiscountAmount().compareTo(BigDecimal.ZERO) == 0) || (orderInfoDTO.getAmount() == null || orderInfoDTO.getAmount().compareTo(BigDecimal.ZERO) == 0)).collect(Collectors.toList());
                orderInfoDTOListForDiscount = orderInfoDTOList.stream().filter(orderInfoDTO -> orderInfoDTO.getMaxDiscountAmount() != null && orderInfoDTO.getMaxDiscountAmount().compareTo(BigDecimal.ZERO) > 0 && orderInfoDTO.getAmount() != null && orderInfoDTO.getAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            }
            //orderAmount和giftAmount比较，取最小值来参与均摊
            BigDecimal averageAmountTotal = orderAmount.min(giftAmount);
            List<OrderInfoVO> orderInfoVOListForDiscountZero = BeanUtil.copyToList(orderInfoDTOListForDiscountZero, OrderInfoVO.class);
            //设置orderInfoVOListForDiscountZero的discountAmount为0
            orderInfoVOListForDiscountZero.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
            orderInfoVOList.addAll(orderInfoVOListForDiscountZero);

            //参与优惠的总价
            BigDecimal totalAmountForDiscount = orderInfoDTOListForDiscount.stream().map(OrderInfoDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (OrderInfoDTO orderInfoDTO : orderInfoDTOListForDiscount) {
                OrderInfoVO actualDiscountAmount = calculateAverageAmount(orderInfoDTO, averageAmountTotal, totalAmountForDiscount, UserAmountTypeEnum.BALANCE.getCode(), tradeType);
                orderInfoVOList.add(actualDiscountAmount);
            }
            log.info("recommendDiscountsForGift userId:{},shopId:{},orderInfoVOList:{}", userId, shopId, JSON.toJSONString(orderInfoVOList));
        } catch (RuntimeException e) {
            log.error("recommendDiscountsForGift error {}", e.getMessage(), e);
            orderInfoVOList = BeanUtil.copyToList(orderInfoDTOList, OrderInfoVO.class);
            //设置orderInfoVOList的discountAmount为0
            orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
        } catch (Exception e) {
            log.error("recommendDiscountsForGift error {}", e.getMessage(), e);
            orderInfoVOList = BeanUtil.copyToList(orderInfoDTOList, OrderInfoVO.class);
            //设置orderInfoVOList的discountAmount为0
            orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
        }
        return orderInfoVOList;
    }

    /**
     * 计算均摊金额
     *
     * @return orderInfoDTO
     * @Param orderInfoDTO 订单信息
     * @Param averageAmountTotal 参与均摊的金额
     * @Param totalAmountForDiscount 参与优惠的商品总价
     * @Param amountType 金额类型 1福豆 2余额
     * @Param tradeType 交易类型 1保税 2大贸
     */
    public OrderInfoVO calculateAverageAmount(OrderInfoDTO orderInfoDTO, BigDecimal averageAmountTotal, BigDecimal totalAmountForDiscount, Integer amountType, Integer tradeType) {
        // 获取单品金额和最高可优惠金额
        BigDecimal amount = orderInfoDTO.getAmount();
        BigDecimal maxDiscountAmount = orderInfoDTO.getMaxDiscountAmount();
        // 计算均摊比例，保留两位小数，除不尽的截断，不然会出现比可用余额多的情况
        BigDecimal allocationRatio = amount.divide(totalAmountForDiscount, 2, RoundingMode.DOWN);
        // 计算均摊金额
        BigDecimal allocatedAmount = averageAmountTotal.multiply(allocationRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal actualDiscountAmount = BigDecimal.ZERO;
        if (amountType.equals(UserAmountTypeEnum.CASH.getCode())) {
            actualDiscountAmount = allocatedAmount;
        } else if (amountType.equals(UserAmountTypeEnum.BALANCE.getCode())) {
            // 计算实际优惠金额，取均摊金额和最大优惠金额的最小值
            actualDiscountAmount = allocatedAmount.min(maxDiscountAmount);
        }
        if (tradeType == 1) {//保税
            BigDecimal itemAmount = minimumItemAmount.multiply(orderInfoDTO.getSkuNum());
            //amount-itemAmount保底金额
            amount = amount.subtract(itemAmount);
        }
        //如果actualDiscountAmount>amount，则actualDiscountAmount=amount
        if (actualDiscountAmount.compareTo(amount) > 0) {
            actualDiscountAmount = amount;
        }
        if (amountType.equals(UserAmountTypeEnum.BALANCE.getCode())) {
            //福豆的优惠金额保留一位小数，其余截断
            actualDiscountAmount = actualDiscountAmount.setScale(1,  RoundingMode.DOWN);
        }
        if(actualDiscountAmount.compareTo(BigDecimal.ZERO) < 0){
            actualDiscountAmount = BigDecimal.ZERO;
        }
        OrderInfoVO orderInfoVO = BeanUtil.copyProperties(orderInfoDTO, OrderInfoVO.class);
        orderInfoVO.setDiscountAmount(actualDiscountAmount);
        return orderInfoVO;
    }


    /**
     * 判断未参与优惠的商品金额是否<0.1
     */
    public boolean isDiscountAmountZero(List<OrderInfoDTO> orderInfoDTOList) {
        return orderInfoDTOList.stream().anyMatch(orderInfoDTO -> (orderInfoDTO.getAmount().divide(orderInfoDTO.getSkuNum(), 2, RoundingMode.DOWN)).compareTo(minimumItemAmount) < 0);
    }

    /**
     * 福卡优惠推荐
     *
     * @return orderInfoVOList
     * @Param orderInfoDTOList 订单信息
     */
    public List<OrderInfoVO> recommendDiscountsForCash(List<OrderInfoDTO> orderInfoDTOList, BigDecimal orderAmount, Long userId, Long shopId, Integer tradeType) {
            List<OrderInfoVO> orderInfoVOList = new ArrayList<>();
        try {
            log.info("福卡优惠推荐 用户ID：" + userId + " 店铺id:" + shopId + "订单金额：" + orderAmount + "订单信息：" + JSON.toJSONString(orderInfoDTOList));
            UserAmountVO userAmountVO = this.userAmountService.detail(userId, shopId);
            BigDecimal cashAmount = new BigDecimal(0);
            if (userAmountVO != null && userAmountVO.getCashAmount() != null && userAmountVO.getCashAmount().compareTo(BigDecimal.ZERO) > 0) {
                cashAmount = userAmountVO.getCashAmount();
            }
            //如果没有福卡，则不参与优惠
            if (cashAmount.compareTo(BigDecimal.ZERO) <= 0) {
                orderInfoVOList = BeanUtil.copyToList(orderInfoDTOList, OrderInfoVO.class);
                //设置orderInfoVOList的discountAmount为0
                orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
                return orderInfoVOList;
            }
            List<OrderInfoDTO> orderInfoDTOListForDiscountZero = new ArrayList<>();
            List<OrderInfoDTO> orderInfoDTOListForDiscount = new ArrayList<>();
            if (tradeType == 1) {//保税
                if (orderAmount.compareTo(minimumOrderAmount) < 0) {
                    log.error("用户ID：" + userId + " 店铺id:" + shopId + "订单金额必须大于：" + minimumOrderAmount);
                    orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
                    return orderInfoVOList;
                }
                //订单金额减去订单保底金额
                orderAmount = orderAmount.subtract(minimumOrderAmount);
                //amount<=0.1的集合
                orderInfoDTOListForDiscountZero = orderInfoDTOList.stream().filter(orderInfoDTO -> orderInfoDTO.getAmount() == null || (orderInfoDTO.getAmount().divide(orderInfoDTO.getSkuNum(), 2, RoundingMode.DOWN)).compareTo(minimumItemAmount) <= 0).collect(Collectors.toList());
                if (isDiscountAmountZero(orderInfoDTOListForDiscountZero)) { //校验amount<0.1的商品
                    log.error("用户ID：" + userId + " 店铺id:" + shopId + "订单中存在金额小于0.1的商品，无法参与优惠！");
                    orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
                    return orderInfoVOList;
                }
                //参与优惠的商品集合 maxDiscountAmount>0的商品且amount>0.1
                orderInfoDTOListForDiscount = orderInfoDTOList.stream().filter(orderInfoDTO -> orderInfoDTO.getAmount() != null && orderInfoDTO.getAmount().compareTo(minimumItemAmount) > 0).collect(Collectors.toList());
            } else {
                orderInfoDTOListForDiscountZero = orderInfoDTOList.stream().filter(orderInfoDTO -> orderInfoDTO.getAmount() == null || orderInfoDTO.getAmount().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
                orderInfoDTOListForDiscount = orderInfoDTOList.stream().filter(orderInfoDTO -> orderInfoDTO.getAmount() != null && orderInfoDTO.getAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            }
            //orderAmount和cashAmount比较，取最小值来参与均摊
            BigDecimal averageAmountTotal = orderAmount.min(cashAmount);
            List<OrderInfoVO> orderInfoVOListForDiscountZero = BeanUtil.copyToList(orderInfoDTOListForDiscountZero, OrderInfoVO.class);
            //设置orderInfoVOListForDiscountZero的discountAmount为0
            orderInfoVOListForDiscountZero.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
            orderInfoVOList.addAll(orderInfoVOListForDiscountZero);

            //参与优惠的总价
            BigDecimal totalAmountForDiscount = orderInfoDTOListForDiscount.stream().map(OrderInfoDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (OrderInfoDTO orderInfoDTO : orderInfoDTOListForDiscount) {
                OrderInfoVO actualDiscountAmount = calculateAverageAmount(orderInfoDTO, averageAmountTotal, totalAmountForDiscount, UserAmountTypeEnum.CASH.getCode(), tradeType);
                orderInfoVOList.add(actualDiscountAmount);
            }
            log.info("recommendDiscountsForCash userId:{},shopId:{},orderInfoVOList:{}", userId, shopId, JSON.toJSONString(orderInfoVOList));
        } catch (RuntimeException e) {
            log.error("recommendDiscountsForCash error", e.getMessage(), e);
            orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
        } catch (Exception e) {
            log.error("recommendDiscountsForCash error", e.getMessage(), e);
            orderInfoVOList.forEach(orderInfoVO -> orderInfoVO.setDiscountAmount(BigDecimal.ZERO));
        }
        return orderInfoVOList;
    }

    /**
     * 福豆、福卡扣减记录
     *
     * @Param userId 用户ID
     * @Param shopId 店铺ID
     * @Param orderId 订单id
     * @Param orderTime 下单时间
     * @Param amount 优惠金额
     * @Param amountTyp 金额类型 1福豆，2福卡
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deductionAmount(Long userId, Long shopId, Long orderId, Long orderTime, BigDecimal amount, Integer amountType) {
        RLock lock = redissonClient.getLock(PARANA_ORDER_AMOUNT_LOCK_KEY + "_" + userId + "_" + shopId + "_" + amountType);
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(50, 50, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new RuntimeException("orderId:" + orderId + " userId:" + userId + ", shopId:" + shopId + ",amountType:" + amountType + " 分布式锁获取失败, 超过等待时间");
            }
            if (shopId == null || shopId == 0) {
                throw new RuntimeException("店铺id不能为空！");
            }
            if (userId == null || userId == 0) {
                throw new RuntimeException("用户id不能为空！");
            }
            if (orderId == null || orderId == 0) {
                throw new RuntimeException("订单id不能为空！");
            }
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new RuntimeException("变更金额必须大于0！");
            }
            if (amountType == null || amountType == 0) {
                throw new RuntimeException("金额类型不能为空！");
            }
            UserAmountVO userAmountVO = this.userAmountService.detailAmount(userId, shopId);
            if (userAmountVO == null) {
                throw new RuntimeException("未查询到余额信息！");
            }
            if (amountType == UserAmountTypeEnum.BALANCE.getCode()) {
                if (userAmountVO.getGiftAmount() == null || amount.compareTo(userAmountVO.getGiftAmount()) > 0) {
                    log.error("福豆余额不足！");
                    return false;
                }
                this.deductionGiftAmount(userId, shopId, orderId, orderTime, amount, userAmountVO.getGiftAmount());
            } else if (amountType == UserAmountTypeEnum.CASH.getCode()) {
                if (userAmountVO.getCashAmount() == null || amount.compareTo(userAmountVO.getCashAmount()) > 0) {
                    log.error("福卡余额不足！");
                    return false;
                }
                deductionCashAmount(userId, shopId, orderId, amount);
            }
            return true;
        } catch (RuntimeException e) {
            log.error("用户ID：" + userId + " 店铺id:" + shopId + "deductionAmount error", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        } catch (Exception e) {
            log.error("用户ID：" + userId + " 店铺id:" + shopId + "deductionAmount error", e.getMessage(), e);
            throw new RuntimeException("userId:" + userId + ", shopId:" + shopId + ",amountType:" + amountType + " 扣减金额失败！");
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
    }

    /**
     * 福豆情况
     *
     * @return boolean
     * @Param userId 用户ID
     * @Param shopId 店铺ID
     * @Param orderId 订单id
     * @Param orderTime 下单时间
     * @Param orderAmount 订单金额
     * @Param balanceAmount 余额
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deductionGiftAmount(Long userId, Long shopId, Long orderId, Long orderTime, BigDecimal orderAmount, BigDecimal balanceAmount) {
        try {
            log.info("福豆扣减：用户ID：" + userId + " 店铺id:" + shopId + "orderAmount:" + orderAmount + " balanceAmount:" + balanceAmount);
            List<OrderDeductionAmountDTO> orderDeductionAmountList = this.selectOrderDeductionList(orderId, userId, shopId, UserAmountTypeEnum.BALANCE.getCode());
            if (CollectionUtils.isNotEmpty(orderDeductionAmountList)) {
                log.error("订单id:" + orderId + "福卡已存在扣减记录！");
                return false;
            }
            BigDecimal orderAlterAmount = orderAmount;
            UserAmountSendQry userAmountSendQry = new UserAmountSendQry();
            userAmountSendQry.setUserId(userId);
            userAmountSendQry.setShopId(shopId);
            userAmountSendQry.setStatus(1);
            userAmountSendQry.setOrderTime(orderTime);
            userAmountSendQry.setAmountType(UserAmountTypeEnum.BALANCE.getCode());
            List<UserAmountSendDTO> userAmountSendDTOList = userAmountSendService.getList(userAmountSendQry);
            if (CollectionUtils.isEmpty(userAmountSendDTOList)) {
                log.error("用户ID：" + userId + " 店铺id:" + shopId + "未查询到福豆券！");
                return false;
            }
            //过滤amount>=0的数据
            userAmountSendDTOList = userAmountSendDTOList.stream().filter(item -> item.getAmount().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userAmountSendDTOList)) {
                log.error("用户ID：" + userId + " 店铺id:" + shopId + "福豆券可用余额为0！");
                return false;
            }
            //对福豆可用余额求和
            BigDecimal sum = userAmountSendDTOList.stream().map(UserAmountSendDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (sum.compareTo(balanceAmount) != 0) {
                log.error("用户ID：" + userId + " 店铺id:" + shopId + "个人福豆余额和福豆券可用余额不一致！");
                return false;
            }
            userAmountSendDTOList.sort(Comparator.comparing(UserAmountSendDTO::getActivityEndTime));
            for (UserAmountSendDTO userAmountSendDTO : userAmountSendDTOList) {
                BigDecimal amount = userAmountSendDTO.getAmount();
                OrderDeductionAmountDTO orderDeductionAmountDTO = new OrderDeductionAmountDTO();
                orderDeductionAmountDTO.setShopId(shopId);
                orderDeductionAmountDTO.setUserId(userId);
                orderDeductionAmountDTO.setOrderId(orderId);
                orderDeductionAmountDTO.setAmountSendId(userAmountSendDTO.getId());
                orderDeductionAmountDTO.setAmountType(UserAmountTypeEnum.BALANCE.getCode());
                if (orderAmount.compareTo(amount) >= 0 && orderAmount.compareTo(BigDecimal.ZERO) > 0 && amount.compareTo(BigDecimal.ZERO) > 0) {
                    orderAmount = orderAmount.subtract(amount);
                    userAmountSendDTO.setAmount(new BigDecimal(0));
                    orderDeductionAmountDTO.setAlterAmount(amount);//福豆券扣减金额
                } else if (orderAmount.compareTo(BigDecimal.ZERO) > 0 && amount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal remainAmount = amount.subtract(orderAmount);
                    userAmountSendDTO.setAmount(remainAmount);//福豆券扣减完余额
                    orderDeductionAmountDTO.setAlterAmount(orderAmount);//福豆券扣减金额
                    orderAmount = new BigDecimal(0);
                }
                if (orderDeductionAmountDTO.getAlterAmount() != null && orderDeductionAmountDTO.getAlterAmount().compareTo(BigDecimal.ZERO) > 0) {
                    //记录订单使用福豆券记录
                    log.info("订单扣减流水orderDeductionAmountDTO:{}", JSON.toJSONString(orderDeductionAmountDTO));
                    this.orderDeductionAmountGateway.insert(orderDeductionAmountDTO);
                    //更新福豆券可用余额
                    log.info("订单扣减userAmountSendDTO:{}", JSON.toJSONString(userAmountSendDTO));
                    this.userAmountSendService.updateAmountSend(userAmountSendDTO);
                }
            }
            //个人可用福豆余额变更
            this.userAmountService.updateAmount(userId, shopId, null, orderAlterAmount, UserAmountTypeEnum.BALANCE.getCode(), 2, UserAmountSendTypeEnum.ORDER_DISCOUNT.getCode(), UserAmountSendTypeEnum.ORDER_DISCOUNT.getName());
        } catch (RuntimeException e) {
            throw new RuntimeException(e.getMessage());
        } catch (Exception e) {
            log.error("用户ID：" + userId + " 店铺id:" + shopId + "福豆扣减异常：{}", e.getMessage(), e);
            throw new RuntimeException("用户ID：" + userId + " 店铺id:" + shopId + "福豆扣减异常!");
        }
        return true;
    }

    /**
     * 福卡情况
     *
     * @param userId
     * @param shopId
     * @param orderId
     * @param orderAmount
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deductionCashAmount(Long userId, Long shopId, Long orderId, BigDecimal orderAmount) {
        try {
            log.info("福卡扣减 用户ID：" + userId + " 店铺id:" + shopId + "orderId:" + orderId + "orderAmount:" + orderAmount);
            List<OrderDeductionAmountDTO> orderDeductionAmountList = this.selectOrderDeductionList(orderId, userId, shopId, UserAmountTypeEnum.CASH.getCode());
            if (CollectionUtils.isNotEmpty(orderDeductionAmountList)) {
                throw new RuntimeException("订单id:" + orderId + "福卡已存在扣减记录！");
            }
            OrderDeductionAmountDTO orderDeductionAmountDTO = new OrderDeductionAmountDTO();
            orderDeductionAmountDTO.setShopId(shopId);
            orderDeductionAmountDTO.setUserId(userId);
            orderDeductionAmountDTO.setOrderId(orderId);
            orderDeductionAmountDTO.setAlterAmount(orderAmount);//福豆券扣减金额
            orderDeductionAmountDTO.setAmountType(UserAmountTypeEnum.CASH.getCode());
            //记录订单使用福豆券记录
            this.orderDeductionAmountGateway.insert(orderDeductionAmountDTO);
            //个人可用福豆余额变更
            this.userAmountService.updateAmount(userId, shopId, null, orderAmount, UserAmountTypeEnum.CASH.getCode(), 2, UserAmountSendTypeEnum.ORDER_DISCOUNT.getCode(), UserAmountSendTypeEnum.ORDER_DISCOUNT.getName());
        } catch (Exception e) {
            log.error("用户ID：" + userId + " 店铺id:" + shopId + "orderId:" + orderId + "福卡扣减异常：{}", e.getMessage(), e);
            throw new RuntimeException("用户ID：" + userId + " 店铺id:" + shopId + "orderId:" + orderId + "福卡扣减异常!");
        }
        return true;
    }


    /**
     * 回退余额
     *
     * @return boolean
     * @Param userId 用户ID
     * @Param shopId 店铺ID
     * @Param orderId 订单号
     * @Param amountTyp 金额类型 1福豆，2福卡
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackBalanceAmount(Long userId, Long shopId, Long orderId, Integer amountType) {
        log.info("订单回退 用户ID：" + userId + " 店铺id:" + shopId + "orderId:" + orderId + "amountType:" + amountType);
        RLock lock = redissonClient.getLock(PARANA_ORDER_AMOUNT_LOCK_KEY + userId + "_" + shopId + "_" + amountType);
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(50, 50, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new RuntimeException("orderId:" + orderId + " userId:" + userId + ", shopId:" + shopId + ",amountType:" + amountType + " 分布式锁获取失败, 超过等待时间");
            }
            if (shopId == null || shopId == 0) {
                throw new RuntimeException("店铺id不能为空！");
            }
            if (userId == null || userId == 0) {
                throw new RuntimeException("用户id不能为空！");
            }
            if (orderId == null || orderId == 0) {
                throw new RuntimeException("订单id不能为空！");
            }
            if (amountType == UserAmountTypeEnum.BALANCE.getCode()) {
                rollbackGiftAmount(userId, shopId, orderId);
            } else if (amountType == UserAmountTypeEnum.CASH.getCode()) {
                rollbackCashAmount(userId, shopId, orderId);
            }
        } catch (RuntimeException e) {
            log.error("回退余额异常：{}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        } catch (Exception e) {
            log.error("回退余额异常：{}", e.getMessage(), e);
            throw new RuntimeException("用户ID：" + userId + " 店铺id:" + shopId + "回退余额异常!");
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return true;
    }

    /**
     * 回退福豆
     *
     * @return boolean
     * @Param userId 用户ID
     * @Param shopId 店铺ID
     * @Param orderId 订单号
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackGiftAmount(Long userId, Long shopId, Long orderId) {
        List<OrderDeductionAmountDTO> orderDeductionAmountDTOList = this.selectOrderDeductionList(orderId, userId, shopId, UserAmountTypeEnum.BALANCE.getCode());
        if (CollectionUtils.isEmpty(orderDeductionAmountDTOList)) {
            throw new RuntimeException("订单id：" + orderId + "福豆没有扣减记录！");
        }
        if (orderDeductionAmountDTOList.stream().anyMatch(orderDeductionAmountDTO -> orderDeductionAmountDTO.getStatus() == 2)) {
            throw new RuntimeException("订单id：" + orderId + "福豆不能重复回退！");
        }
        List<Long> ids = orderDeductionAmountDTOList.stream().map(OrderDeductionAmountDTO::getAmountSendId).collect(Collectors.toList());
        UserAmountSendQry userAmountSendQry = new UserAmountSendQry();
        userAmountSendQry.setIds(ids);
        List<UserAmountSendDTO> userAmountSendDTOList = userAmountSendService.getList(userAmountSendQry);
        if (CollectionUtils.isEmpty(userAmountSendDTOList)) {
            throw new RuntimeException("ids：" + ids + "没有查询到福豆券！");
        }
        BigDecimal orderAmount = new BigDecimal(0); //订单回退总金额
        for (OrderDeductionAmountDTO orderDeductionAmountDTO : orderDeductionAmountDTOList) {
            for (UserAmountSendDTO userAmountSendDTO : userAmountSendDTOList) {
                if (orderDeductionAmountDTO.getAmountSendId().equals(userAmountSendDTO.getId())) {
                    BigDecimal alterAmount = orderDeductionAmountDTO.getAlterAmount();
                    BigDecimal sendAmount = userAmountSendDTO.getSendAmount();
                    BigDecimal amount = userAmountSendDTO.getAmount().add(alterAmount);
                    if (sendAmount.compareTo(amount) < 0) {
                        throw new RuntimeException("福豆券id：" + userAmountSendDTO.getId() + "回退异常,福豆券可用余额大于发送余额!");
                    }
                    userAmountSendDTO.setAmount(amount);
                    if (System.currentTimeMillis() <= userAmountSendDTO.getActivityEndTime()) {
                        orderAmount = orderAmount.add(alterAmount);//生效中的才回退到个人余额
                    }
                    orderDeductionAmountDTO.setStatus(2); //回退
                    //标记回退
                    log.info("标记回退orderDeductionAmountDTO:{}", JSON.toJSONString(orderDeductionAmountDTO));
                    this.orderDeductionAmountGateway.update(orderDeductionAmountDTO);
                    //回退福豆券余额
                    log.info("回退福豆券余额userAmountSendDTO:{}", JSON.toJSONString(userAmountSendDTO));
                    this.userAmountSendService.updateAmountSend(userAmountSendDTO);
                }
            }
        }
        //更新用户余额
        if (orderAmount.compareTo(BigDecimal.ZERO) > 0) {
            this.userAmountService.updateAmount(userId, shopId, null, orderAmount, UserAmountTypeEnum.BALANCE.getCode(), 1, UserAmountSendTypeEnum.REFUND_RETURN.getCode(), UserAmountSendTypeEnum.REFUND_RETURN.getName());
        }
        return true;
    }

    /**
     * 查询订单扣减记录
     */
    public List<OrderDeductionAmountDTO> selectOrderDeductionList(Long orderId, Long userId, Long shopId, Integer amountType) {
        OrderDeductionAmountQry orderDeductionAmountQry = new OrderDeductionAmountQry();
        orderDeductionAmountQry.setOrderId(orderId);
        orderDeductionAmountQry.setUserId(userId);
        orderDeductionAmountQry.setShopId(shopId);
        orderDeductionAmountQry.setAmountType(amountType);
        return this.orderDeductionAmountGateway.selectList(orderDeductionAmountQry);
    }

    /**
     * 回退福卡
     *
     * @return boolean
     * @Param userId
     * @Param shopId
     * @Param orderId
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackCashAmount(Long userId, Long shopId, Long orderId) {
        OrderDeductionAmountQry orderDeductionAmountQry = new OrderDeductionAmountQry();
        orderDeductionAmountQry.setOrderId(orderId);
        orderDeductionAmountQry.setUserId(userId);
        orderDeductionAmountQry.setShopId(shopId);
        orderDeductionAmountQry.setAmountType(UserAmountTypeEnum.CASH.getCode());
        List<OrderDeductionAmountDTO> orderDeductionAmountDTOList = this.orderDeductionAmountGateway.selectList(orderDeductionAmountQry);
        if (CollectionUtils.isEmpty(orderDeductionAmountDTOList)) {
            throw new RuntimeException("订单id：" + orderId + "福卡没有扣减记录！");
        }
        if (orderDeductionAmountDTOList.stream().anyMatch(orderDeductionAmountDTO -> orderDeductionAmountDTO.getStatus() == 2)) {
            throw new RuntimeException("订单id：" + orderId + "福卡不能重复回退！");
        }
        BigDecimal orderAmount = new BigDecimal(0); //订单回退总金额
        for (OrderDeductionAmountDTO orderDeductionAmountDTO : orderDeductionAmountDTOList) {
            BigDecimal alterAmount = orderDeductionAmountDTO.getAlterAmount();
            orderAmount = orderAmount.add(alterAmount);
            orderDeductionAmountDTO.setStatus(2); //回退
        }
        //标记回退
        this.orderDeductionAmountGateway.updateBatch(orderDeductionAmountDTOList);
        //更新用户余额
        this.userAmountService.updateAmount(userId, shopId, null, orderAmount, UserAmountTypeEnum.CASH.getCode(), 1, UserAmountSendTypeEnum.REFUND_RETURN.getCode(), UserAmountSendTypeEnum.REFUND_RETURN.getName());
        return true;
    }

}