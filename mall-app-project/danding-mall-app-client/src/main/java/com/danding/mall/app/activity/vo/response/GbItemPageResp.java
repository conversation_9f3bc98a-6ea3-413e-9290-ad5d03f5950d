package com.danding.mall.app.activity.vo.response;

import com.danding.mall.app.items.dto.data.ItemsDTO;
import com.danding.mall.app.items.vo.response.other.ItemVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class GbItemPageResp implements Serializable {

    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "是否已参与")
    private Boolean selected;
    @ApiModelProperty(value = "不可参与原因")
    private String unSelectReason;
    @ApiModelProperty(value = "商品信息")
    private ItemVO itemDTO;



}
