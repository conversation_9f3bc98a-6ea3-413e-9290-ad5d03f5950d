package com.danding.mall.app.activity.vo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ItemResp implements Serializable {

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "商品编码(可能外部)")
    private String itemCode;

//    @ApiModelProperty(value = "后台类目 ID")
//    private Long categoryId;
//
//    @ApiModelProperty(value = "SPU编号")
//    private Long spuId;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

//    @ApiModelProperty(value = "是否保税(1:保税，0:完税)")
//    private Integer isBonded;
//
//    @ApiModelProperty(value = "是否为第三方商品（0：为自建商品，1：第三方商品）")
//    private Integer isThirdPartyItem;

    @ApiModelProperty(value = "商品来源(1：代塔仓自有，2：京东云交易)")
    private Integer sourceType;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

//    @ApiModelProperty(value = "品牌id")
//    private Long brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品简介")
    private String intro;

    @ApiModelProperty(value = "主图")
    private String mainImage;

//    @ApiModelProperty(value = "实际售卖价格(所有sku的最低实际售卖价格)")
//    private Integer lowPrice;
//
//    @ApiModelProperty(value = "实际售卖价格(所有sku的最高实际售卖价格)")
//    private Integer highPrice;

//    @ApiModelProperty(value = "库存类型, 0: 不分仓存储, 1: 分仓存储")
//    private Integer stockType;

    @ApiModelProperty(value = "库存")
    private Integer stockQuantity;

    @ApiModelProperty(value = "销量")
    private Integer saleQuantity;

//    @ApiModelProperty(value = "状态")
//    private Integer status;

//    @ApiModelProperty(value = "上架时间")
//    private Date onShelfAt;

    @ApiModelProperty(value = "广告语")
    private String advertise;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "商品类型 1为普通商品, 2为组合商品")
    private Integer type;

    @ApiModelProperty(value = "是否参与活动")
    private Integer tips;

//    @ApiModelProperty(value = "活动图文开关 (0-不开启 1-开启)")
//    private Integer activityImageSwitch;

//    @ApiModelProperty(value = "减库存方式, 1为拍下减库存, 2为付款减库存")
//    private Integer reduceStockType;

//    @ApiModelProperty(value = "商品额外信息,建议json字符串")
//    private String extraJson;
//
//    @ApiModelProperty(value = "商品标签的json表示形式,只能运营操作, 对商家不可见")
//    private String tagsJson;

//    @ApiModelProperty(value = "商品信息的m5值, 商品快照需要和这个摘要进行对比")
//    private String itemInfoMd5;

//    @ApiModelProperty(value = "")
//    private Date createdAt;
//
//    @ApiModelProperty(value = "")
//    private Date updatedAt;
//
//    @ApiModelProperty(value = "")
//    private Integer index;

    @ApiModelProperty(value = "")
    private String barCode;
//
//    @ApiModelProperty(value = "售罄状态(1-未售罄，2-已售罄)")
//    private Integer sellOutStatus;

//    @ApiModelProperty(value = "区域限售模板id")
//    private Long restrictedSalesAreaTemplateId;

//    @ApiModelProperty(value = "图片列表, json表示（辅图）")
//    private String imagesJson;
//
//    @ApiModelProperty(value = "富文本详情")
//    private String detail;

    @ApiModelProperty(value = "详情地址")
    private List<String> detailImageList;


    @ApiModelProperty(value = "最低活动价(所有sku的最低实际售卖价格)")
    private BigDecimal gbMinPrice;

    @ApiModelProperty(value = "最高活动价(所有sku的最高实际售卖价格)")
    private BigDecimal gbMaxPrice;

    @ApiModelProperty(value = "实际售卖价格(所有sku的最低实际售卖价格)")
    private BigDecimal itemLowPrice;

    @ApiModelProperty(value = "实际售卖价格(所有sku的最高实际售卖价格)")
    private BigDecimal itemHighPrice;
}
