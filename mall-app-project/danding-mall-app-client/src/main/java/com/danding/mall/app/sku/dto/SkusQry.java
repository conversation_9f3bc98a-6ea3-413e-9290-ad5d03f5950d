package com.danding.mall.app.sku.dto;

import com.danding.core.client.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商品SKU表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Skus对象", description="商品SKU表")
public class SkusQry extends BaseQry {

    private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "")
        private Long id;
        private List<Long> idList;
        @ApiModelProperty(value = "SKU 编码 (标准库存单位编码)")
        private String skuCode;
        @ApiModelProperty(value = "商品id")
        private Long itemId;
        private List<Long> itemIds;
        @ApiModelProperty(value = "店铺 ID (冗余自商品表)")
        private Long shopId;
        @ApiModelProperty(value = "是否保税(1:保税，0:完税)")
        private Integer type;
        @ApiModelProperty(value = "商品状态 (冗余自商品表)")
        private Integer status;
        private Integer notStatus;
        @ApiModelProperty(value = "型号/款式")
        private String specification;
        @ApiModelProperty(value = "外部sku编号")
        private String outerSkuId;
        @ApiModelProperty(value = "外部店铺id")
        private String outerShopId;
        @ApiModelProperty(value = "图片url")
        private String image;
        @ApiModelProperty(value = "名称")
        private String name;
        @ApiModelProperty(value = "sku其他各种价格的json表示形式")
        private String extraPriceJson;
        @ApiModelProperty(value = "实际售卖价格(低)")
        private Integer price;
        @ApiModelProperty(value = "json存储的sku属性键值对")
        private String attrsJson;
        @ApiModelProperty(value = "库存类型, 0: 不分仓存储, 1: 分仓存储, (冗余自商品表)")
        private Integer stockType;
        @ApiModelProperty(value = "库存")
        private Integer stockQuantity;
    @ApiModelProperty(value = "销量")
    private Integer saleQuantity;

    @ApiModelProperty(value = "版本号")
    private Integer version;

        @ApiModelProperty(value = "sku额外信息")
        private String extra;
        @ApiModelProperty(value = "tag信息,json表示")
        private String tagsJson;
        @ApiModelProperty(value = "")
        private Date createdAt;
        @ApiModelProperty(value = "")
        private Date updatedAt;
        @ApiModelProperty(value = "样本图 (SKU 缩略图) URL")
        private String thumbnail;
        @ApiModelProperty(value = "")
        private Long guiderProfitRate;
        @ApiModelProperty(value = "")
        private Long guiderProfitFee;
        @ApiModelProperty(value = "")
        private Long subStoreProfitRate;
        @ApiModelProperty(value = "")
        private Long subStoreProfitFee;
        @ApiModelProperty(value = "")
        private String depotCode;
        @ApiModelProperty(value = "SKU 编码集合查询")
        private List<Long> skus;

        @ApiModelProperty(value = "活动销售价开关 (0-不开启 1-开启)")
        private Integer activitySalesPriceSwitch;
}
