package com.danding.mall.app.sku.dto.data;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.danding.core.client.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* <p>
    * 组合商品关联表
    * </p>
*
* <AUTHOR>
* @since 2025-04-12
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SkuComboRelation对象", description="组合商品关联表")
public class SkuComboRelationDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "组合里的商品Id")
    private Long comboItemId;

    @ApiModelProperty(value = "组合里的商品名称")
    private String comboItemName;

    @ApiModelProperty(value = "组合里的skuId")
    private Long comboSkuId;

    @ApiModelProperty(value = "组合skuId数量")
    private Integer comboSkuQuantity;

    @ApiModelProperty(value = "乐观锁")
    private Integer version;

    @ApiModelProperty(value = "创建人（用户id）")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "修改人（用户id）")
    private String updatedBy;

    @ApiModelProperty(value = "修改时间")
    private Long updatedTime;

    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "是否已删除", required = false, hidden = true)
    protected Integer deleted;

    @Override
    public void setId(Long id) {
        this.id = id;
    }

}
