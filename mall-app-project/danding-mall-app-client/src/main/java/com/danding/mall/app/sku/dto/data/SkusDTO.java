package com.danding.mall.app.sku.dto.data;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.danding.core.client.BaseDTO;
import com.danding.mall.app.items.dto.attribute.SkuAttribute;
import com.danding.mall.app.items.model.JacksonType;
import com.danding.mall.app.sku.enums.SkuExtraIndex;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import joptsimple.internal.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <p>
    * 商品SKU表
    * </p>
*
* <AUTHOR>
* @since 2025-02-28
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Skus对象", description="商品SKU表")
public class SkusDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;

    private static final ObjectMapper objectMapper = new ObjectMapper();


    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "SKU 编码 (标准库存单位编码)")
    private String skuCode;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "店铺 ID (冗余自商品表)")
    private Long shopId;

    @ApiModelProperty(value = "是否保税(1:保税，0:完税)")
    private Integer type;

    @ApiModelProperty(value = "商品状态 (冗余自商品表)")
    private Integer status;

    @ApiModelProperty(value = "型号/款式")
    private String specification;

    @ApiModelProperty(value = "外部sku编号")
    private String outerSkuId;

    @ApiModelProperty(value = "外部店铺id")
    private String outerShopId;

    @ApiModelProperty(value = "图片url")
    private String image;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "sku其他各种价格的json表示形式")
    private String extraPriceJson;

    @ApiModelProperty(value = "实际售卖价格(低)")
    private Integer price;

    @ApiModelProperty(value = "json存储的sku属性键值对")
    private String attrsJson;

    @ApiModelProperty(value = "库存类型, 0: 不分仓存储, 1: 分仓存储, (冗余自商品表)")
    private Integer stockType;

    @ApiModelProperty(value = "初始库存数量")
    private Integer initialStockQuantity;

    @ApiModelProperty(value = "库存")
    private Integer stockQuantity;

    @ApiModelProperty("销量")
    private Integer saleQuantity;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "对应的规格列表")
    private List<String> specDetails;

    @ApiModelProperty(value = "sku额外信息")
    private String extra;

    @ApiModelProperty(value = "tag信息,json表示")
    private String tagsJson;

    @ApiModelProperty(value = "")
    private Date createdAt;

    @ApiModelProperty(value = "")
    private Date updatedAt;

    @ApiModelProperty(value = "样本图 (SKU 缩略图) URL")
    private String thumbnail;

    @ApiModelProperty(value = "")
    private Long guiderProfitRate;

    @ApiModelProperty(value = "")
    private Long guiderProfitFee;

    @ApiModelProperty(value = "")
    private Long subStoreProfitRate;

    @ApiModelProperty(value = "")
    private Long subStoreProfitFee;

    @ApiModelProperty(value = "")
    private String depotCode;

    @ApiModelProperty(value = "活动销售价开关 (0-不开启 1-开启)")
    private Integer activitySalesPriceSwitch;


    private Map<String, String> extraMap;

    private Map<String, Integer> extraPrice;

    private Map<String, String> tags;

    /**
     * sku的销售属性, 不存数据库
     */
    @Getter
    private List<SkuAttribute> attrs;

    @ApiModelProperty(value = "活动类型")
    private Integer marketingToolId;

    @ApiModelProperty(value = "活动名")
    private String activityName;
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "购物车-冗余价格")
    private BigDecimal skuPrice;

    @ApiModelProperty(value = "划线价-冗余")
    private BigDecimal crossedPrice;

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @SneakyThrows
    public void setExtra(String extra) {
        this.extra = extra;
        if (StringUtils.isEmpty(extra)) {
            this.extraMap = Collections.emptyMap();
        } else {
            this.extraMap = objectMapper.readValue(extra, JacksonType.MAP_OF_STRING);
        }
    }

    @SneakyThrows
    public Map<String,String> getExtraMap() {
        if (StrUtil.isBlank(this.extra)) {
            this.extraMap = Collections.emptyMap();
        }else {
            this.extraMap = objectMapper.readValue(extra, JacksonType.MAP_OF_STRING);
        }
        return extraMap;
    }

    /**
     * 前端会依赖：com.danding.mall.app.controller.item.ItemsController#skuInfoByItemId
     * @return
     */
    public boolean isExtraActivityValid() {
        if (CollectionUtils.isEmpty(this.getExtraMap())) {
            return false;
        }

        //开始时间与结束时间任一为空，则活动无效
        String activityStart = this.getExtraMap().get(SkuExtraIndex.activityStartTime.name());
        String activityEnd = this.getExtraMap().get(SkuExtraIndex.activityEndTime.name());
        if (!StringUtils.hasText(activityStart) || !StringUtils.hasText(activityEnd)) {
            return false;
        }

        //当前时间须处于开始时与结束时间的范围内，否则活动无效
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Date start = simpleDateFormat.parse(activityStart);
            Date end = simpleDateFormat.parse(activityEnd);
            Date current = new Date();

            return start.before(current) && current.before(end);
        } catch (Exception ex) {
            return false;
        }
    }

    @SneakyThrows
    public void setExtraPriceJson(String extraPriceJson) {
        this.extraPriceJson = extraPriceJson;
        if (StringUtils.isEmpty(extraPriceJson)) {
            this.extraPrice = Collections.emptyMap();
        } else {
            this.extraPrice = objectMapper.readValue(extraPriceJson, JacksonType.MAP_OF_INTEGER);
        }
    }


    public void setTagsJson(String tagsJson) throws Exception {
        this.tagsJson = tagsJson;
        if (Strings.isNullOrEmpty(tagsJson)) {
            this.tags = Collections.emptyMap();
        } else {
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }



    /************* map 转 json **********/

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if (CollectionUtils.isEmpty(tags)) {
            this.tagsJson = "";
        } else {
            this.tagsJson = JSONObject.toJSONString(tags);
        }
    }

    public void setExtraMap(Map<String, String> extraMap) {
        this.extraMap = extraMap;
        if (CollectionUtils.isEmpty(extraMap)) {
            this.extra = "";
        } else {
            this.extra = JSONObject.toJSONString(extraMap);
        }
    }

    public void setExtraPrice(Map<String, Integer> extraPrice) {
        this.extraPrice = extraPrice;
        if (CollectionUtils.isEmpty(extraPrice)) {
            this.extraPriceJson = "";
        } else {
            this.extraPriceJson = JSONObject.toJSONString(extraPrice);
        }
    }
}
