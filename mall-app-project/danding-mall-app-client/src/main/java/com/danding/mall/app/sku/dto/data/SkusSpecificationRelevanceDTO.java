package com.danding.mall.app.sku.dto.data;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.danding.core.client.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* <p>
    * sku规格关联表
    * </p>
*
* <AUTHOR>
* @since 2025-04-08
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SkusSpecificationRelevance对象", description="sku规格关联表")
public class SkusSpecificationRelevanceDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "规格详情id")
    private Long specDetailId;

    @ApiModelProperty(value = "规格名称")
    private String specName;

    @ApiModelProperty(value = "规格详情前端生成的id")
    private String specDetailFrontId;

    @ApiModelProperty(value = "规格的顺序")
    private Integer sequence;

    @ApiModelProperty(value = "乐观锁")
    private Integer version;

    @ApiModelProperty(value = "创建人（用户id）")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "修改人（用户id）")
    private String updatedBy;

    @ApiModelProperty(value = "修改时间")
    private Long updatedTime;

    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "是否已删除", required = false, hidden = true)
    protected Integer deleted;

    @Override
    public void setId(Long id) {
        this.id = id;
    }

}
