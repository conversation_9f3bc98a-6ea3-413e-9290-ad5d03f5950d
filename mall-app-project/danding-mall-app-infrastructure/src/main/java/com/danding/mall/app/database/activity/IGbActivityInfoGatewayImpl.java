package com.danding.mall.app.database.activity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.core.domain.base.BaseServiceImpl;
import com.danding.core.domain.utils.ConverterUtil;
import com.danding.mall.app.activity.dto.GbGroupMemberQry;
import com.danding.mall.app.activity.dto.data.*;
import com.danding.mall.app.activity.enums.GbActivityOperateTypeEnum;
import com.danding.mall.app.activity.enums.GbActivityStatusEnum;
import com.danding.mall.app.activity.enums.GbGroupMemberStatusEnum;
import com.danding.mall.app.activity.vo.param.GbActivityParam;
import com.danding.mall.app.activity.vo.request.GbActivityConfigRewardReq;
import com.danding.mall.app.activity.vo.response.GbActivityConfigRewardResp;
import com.danding.mall.app.base.session.helper.ShopHelper;
import com.danding.mall.app.base.session.helper.UserHelper;
import com.danding.mall.app.database.activity.entity.*;
import com.danding.mall.app.database.activity.mapper.*;
import com.danding.mall.app.domain.activity.gateway.IGbActivityInfoGateway;
import com.danding.mall.app.database.activity.util.GbActivityInfoUtil;
import com.danding.mall.app.activity.dto.GbActivityInfoQry;
import com.danding.mall.app.domain.activity.gateway.IGbGroupMemberGateway;
import com.danding.mall.app.domain.inventory.gateway.IMerchantCommodityInventoryGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 拼团活动表，gp全程为Group buying 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Component
public class IGbActivityInfoGatewayImpl extends BaseServiceImpl<
        GbActivityInfoMapper,
        GbActivityInfo,
        GbActivityInfoDTO,
        GbActivityInfoQry> implements IGbActivityInfoGateway {

    @Resource
    private GbActivityInfoMapper gbActivityInfoMapper;
    @Resource
    private GbActivityConfigSkuMapper gbActivityConfigSkuMapper;
    @Resource
    private GbActivityConfigRewardMapper gbActivityConfigRewardMapper;
    @Resource
    private GbActivityConfigExtendMapper gbActivityConfigExtendMapper;
    @Resource
    private GbActivityOperateMapper gbActivityOperateMapper;
    @Resource
    private IGbGroupMemberGateway gbGroupMemberGateway;

    @Resource
    private IMerchantCommodityInventoryGateway merchantCommodityInventoryGateway;

    @Override
    public LambdaQueryWrapper<GbActivityInfo> getCommonWrapper(GbActivityInfoQry param) {
        return GbActivityInfoUtil.getQueryWrapper(param, baseWrapper(param));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createActivity(GbActivityParam gbActivityParam) {
        // 1. 更新活动信息
        GbActivityInfo info = ConverterUtil.convert(gbActivityParam, GbActivityInfo.class);
        info.setShopId(ShopHelper.getId());
        info.setActivityStatus(GbActivityStatusEnum.NEW.getCode());
        gbActivityInfoMapper.insert(info);
        // 2. 新增活动关联商品信息
        List<GbActivityConfigSkuDTO> skuList = gbActivityParam.getSkuList();
        //2.1 补全商品列表中的店铺id，并执行单条插入
        skuList.forEach(sku -> {
            GbActivityConfigSku gbSku = ConverterUtil.convert(sku, GbActivityConfigSku.class);
            gbSku.setShopId(ShopHelper.getId()).setActivityId(info.getId());
            gbSku.setMarketingToolId(info.getMarketingToolId());
            gbActivityConfigSkuMapper.insert(gbSku);

        });
        // 3. 新增活动奖励信息
        //3.1 补全奖励列表中的店铺id，并执行单条插入
        List<GbActivityConfigRewardReq> rewardList = gbActivityParam.getRewardList();
        rewardList.forEach(reward -> {
            GbActivityConfigReward re = ConverterUtil.convert(reward, GbActivityConfigReward.class);
            re.setCouponIds(reward.getCouponIdsStr());
            re.setShopId(ShopHelper.getId()).setActivityId(info.getId());
            Integer fudouExpireDay = reward.getFudouExpireDay();
            if(fudouExpireDay != null){
                re.setFudouExpireHours(fudouExpireDay * 24);
            }
            gbActivityConfigRewardMapper.insert(re);
        });
        // 4. 新增活动动态配置信息,初始100，后续可数据库或者页面修改
        GbActivityConfigExtend extend = new GbActivityConfigExtend();
        extend.setShopId(ShopHelper.getId());
        extend.setActivityId(info.getId());
        extend.setParaCode("init_group_count");
        extend.setParaName("初始成团参数");
        extend.setParaValue("0");
        gbActivityConfigExtendMapper.insert(extend);
        //5. 添加活动生命周期记录
        GbActivityOperate operate = new GbActivityOperate();
        operate.setActivityId(info.getId());
        operate.setOperateType(GbActivityOperateTypeEnum.ACTIVITY_CREATION.getCode());
        operate.setOperateContent("新建活动");
        operate.setOperateUser(UserHelper.getUserId());
        operate.setOperateTime(System.currentTimeMillis());
        gbActivityOperateMapper.insert(operate);
        return info.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActivityBeforeStart(GbActivityParam gbActivityParam) {
        Long shopId = ShopHelper.getId();
        // 1. 更新活动信息
        GbActivityInfo info = ConverterUtil.convert(gbActivityParam, GbActivityInfo.class);
        info.setShopId(ShopHelper.getId());
        gbActivityInfoMapper.updateById(info);

        Long activityId = gbActivityParam.getId();
        // 2. 更新活动关联商品信息
        List<GbActivityConfigSkuDTO> skuList = gbActivityParam.getSkuList();


        List<Long> newIdList = new ArrayList<>();
        //2.1 补全商品列表中的店铺id
        skuList.forEach(sku -> {
            GbActivityConfigSku gbSku = ConverterUtil.convert(sku, GbActivityConfigSku.class);
            if(gbSku.getId() != null){
                // 页面上不会修改库存
                gbActivityConfigSkuMapper.updateById(gbSku);
            }else {
                gbSku.setShopId(ShopHelper.getId()).setActivityId(info.getId());
                gbSku.setMarketingToolId(info.getMarketingToolId());
                gbActivityConfigSkuMapper.insert(gbSku);
            }
            newIdList.add(gbSku.getId());
        });

        // 数据库中删除所有在参数中的sku 剩余的就是要删除的
        List<Long> removeSkuIdList = gbActivityParam.getRemoveSkuIdList();
        if(!CollectionUtils.isEmpty(removeSkuIdList)){
            log.info("删除SKU {}", JSONObject.toJSONString(removeSkuIdList));
            LambdaQueryWrapper<GbActivityConfigSku> configSkuDeleteWrapper = new LambdaQueryWrapper<GbActivityConfigSku>();
            configSkuDeleteWrapper.in(GbActivityConfigSku::getSkuId, removeSkuIdList);
            configSkuDeleteWrapper.in(GbActivityConfigSku::getActivityId, activityId);
            gbActivityConfigSkuMapper.delete(configSkuDeleteWrapper);

            // 释放库存
            for (Long skuId : removeSkuIdList) {
                log.info("删除SKU释放库存 {} {}", activityId, skuId);
                merchantCommodityInventoryGateway.release(shopId, activityId, skuId);
            }
        }

        // 3. 更新活动奖励信息
        //3.1 补全奖励列表中的店铺id，并执行单条插入
        List<GbActivityConfigRewardReq> rewardList = gbActivityParam.getRewardList();
        rewardList.forEach(reward -> {
            GbActivityConfigReward rewardInfo = ConverterUtil.convert(reward, GbActivityConfigReward.class);
            rewardInfo.setCouponIds(reward.getCouponIdsStr());
            if(reward.getFudouExpireDay() != null){
                rewardInfo.setFudouExpireHours(reward.getFudouExpireDay() * 24);
            }
            if(rewardInfo.getId() != null){
                gbActivityConfigRewardMapper.updateById(rewardInfo);
            }else {
                rewardInfo.setShopId(ShopHelper.getId()).setActivityId(info.getId());
                gbActivityConfigRewardMapper.insert(rewardInfo);
            }
        });
        //4. 添加活动生命周期记录
        GbActivityOperate operate = new GbActivityOperate();
        operate.setActivityId(info.getId());
        operate.setOperateType(GbActivityOperateTypeEnum.INFO_MODIFICATION_BEFORE_START.getCode());
        operate.setOperateContent("信息修改");
        operate.setOperateUser(UserHelper.getUserId());
        operate.setOperateTime(System.currentTimeMillis());
        gbActivityOperateMapper.insert(operate);
    }

    @Override
    public void updateActivityAfterStart(GbActivityParam gbActivityParam) {
        GbActivityInfo info = ConverterUtil.convert(gbActivityParam, GbActivityInfo.class);
        List<GbActivityConfigSkuDTO> skuList = gbActivityParam.getSkuList();
        skuList.forEach(sku -> {
            GbActivityConfigSku gbSku = ConverterUtil.convert(sku, GbActivityConfigSku.class);
            if(gbSku.getId() != null){
                gbActivityConfigSkuMapper.updateById(gbSku);
            }else {
                gbSku.setShopId(ShopHelper.getId()).setActivityId(info.getId());
                gbSku.setMarketingToolId(info.getMarketingToolId());
                gbActivityConfigSkuMapper.insert(gbSku);
            }
        });
    }

    @Override
    public GbActivityDetailDTO detailById(Long activityId) {
        GbActivityDetailDTO gbActivityDetailDTO = new GbActivityDetailDTO();
        //1. 获取活动基本信息
        GbActivityInfo gbActivityInfo = gbActivityInfoMapper.selectById(activityId);
        GbActivityInfoDTO gbActivityInfoDTO = ConverterUtil.convert(gbActivityInfo, GbActivityInfoDTO.class);
        gbActivityDetailDTO.setGbActivityInfoDTO(gbActivityInfoDTO);
        //2. 检索活动绑定商品信息
        LambdaQueryWrapper<GbActivityConfigSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GbActivityConfigSku::getActivityId, activityId);
        List<GbActivityConfigSku> gbActivityConfigSkus = gbActivityConfigSkuMapper.selectList(wrapper);
        List<GbActivityConfigSkuDTO> gbActivityConfigSkuDTOS = ConverterUtil.convertList(gbActivityConfigSkus, GbActivityConfigSkuDTO.class);
        gbActivityDetailDTO.setGbActivityConfigSkuDTOS(gbActivityConfigSkuDTOS);
        //3. 检索活动绑定奖励信息
        LambdaQueryWrapper<GbActivityConfigReward> rewardWrapper = new LambdaQueryWrapper<>();
        rewardWrapper.in(GbActivityConfigReward::getActivityId, activityId);
        List<GbActivityConfigReward> gbActivityConfigRewards = gbActivityConfigRewardMapper.selectList(rewardWrapper);
        List<GbActivityConfigRewardResp> gbActivityConfigRewardDTOS = gbActivityConfigRewards.stream().map(dto -> {
            GbActivityConfigRewardResp rewardResp = ConverterUtil.convert(dto, GbActivityConfigRewardResp.class);
            String couponIds = dto.getCouponIds();
            if(!StringUtils.isEmpty(couponIds)){
                rewardResp.setCouponIds(Arrays.asList(couponIds.split(";")));
            }
            if(dto.getFudouExpireHours() != null){
                rewardResp.setFudouExpireDay(dto.getFudouExpireHours()/24);
            }
            return rewardResp;
        }).collect(Collectors.toList());
        gbActivityDetailDTO.setGbActivityConfigRewardDTOS(gbActivityConfigRewardDTOS);
        //4. 检索活动绑定参数信息
        LambdaQueryWrapper<GbActivityConfigExtend> extendWrapper = new LambdaQueryWrapper<>();
        extendWrapper.in(GbActivityConfigExtend::getActivityId, activityId);
        List<GbActivityConfigExtend> gbActivityConfigExtends = gbActivityConfigExtendMapper.selectList(extendWrapper);
        List<GbActivityConfigExtendDTO> gbActivityConfigExtendDTOS = ConverterUtil.convertList(gbActivityConfigExtends, GbActivityConfigExtendDTO.class);
        gbActivityDetailDTO.setGbActivityConfigExtendDTOS(gbActivityConfigExtendDTOS);
        return gbActivityDetailDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startActivity(Long activityId) {
        // 0. 只能针对未开始/暂停的活动进行操作
        GbActivityInfo gbActivityInfo = gbActivityInfoMapper.selectById(activityId);
        GbActivityStatusEnum status = GbActivityStatusEnum.fromCode(gbActivityInfo.getActivityStatus());
        switch (status) {
            case IN_PROGRESS:
                throw new RuntimeException("活动进行中，无需再次开启！");
            case CLEARED:
                throw new RuntimeException("活动已结清，无法开启活动！");
            case EXPIRED:
                throw new RuntimeException("活动已过期，无法开启活动！");
            default:
                // 1. 更新活动信息
                GbActivityInfo info = new GbActivityInfo();
                info.setId(activityId);
                info.setActivityStatus(GbActivityStatusEnum.IN_PROGRESS.getCode());
                gbActivityInfoMapper.updateById(info);
                // 2. 添加活动记录
                GbActivityOperate operate = new GbActivityOperate();
                operate.setActivityId(info.getId());
                operate.setOperateType(GbActivityOperateTypeEnum.START_ACTIVITY.getCode());
                operate.setOperateContent(GbActivityOperateTypeEnum.START_ACTIVITY.getDescription());
                operate.setOperateUser(UserHelper.getUserId());
                operate.setOperateTime(System.currentTimeMillis());
                gbActivityOperateMapper.insert(operate);
                break;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pauseActivity(Long activityId) {
        handlePauseActivity(activityId);
    }

    public void handlePauseActivity(Long activityId){
        // 1. 只能针对进行中的的活动进行操作
        GbActivityInfo gbActivityInfo = gbActivityInfoMapper.selectById(activityId);
        GbActivityStatusEnum status = GbActivityStatusEnum.fromCode(gbActivityInfo.getActivityStatus());
        switch (status) {
            case IN_PROGRESS:
                /// 0. 失效时不操作库存
//                LambdaQueryWrapper<GbActivityConfigSku> configSkuWrapper = new LambdaQueryWrapper<>();
//                configSkuWrapper.eq(GbActivityConfigSku::getActivityId, activityId);
//                List<GbActivityConfigSku> configSkuList = gbActivityConfigSkuMapper.selectList(configSkuWrapper);
//                releaseConfigSkuInventory(activityId, configSkuList, "作废");

                // 1. 更新活动信息
                GbActivityInfo info = new GbActivityInfo();
                info.setId(activityId);
                info.setActivityStatus(GbActivityStatusEnum.INVALID.getCode());
                gbActivityInfoMapper.updateById(info);
                // 2. 添加活动记录
                GbActivityOperate operate = new GbActivityOperate();
                operate.setActivityId(info.getId());
                operate.setOperateType(GbActivityOperateTypeEnum.MANUAL_INVALIDATION.getCode());
                operate.setOperateContent(GbActivityOperateTypeEnum.MANUAL_INVALIDATION.getDescription());
                operate.setOperateUser(UserHelper.getUserId());
                operate.setOperateTime(System.currentTimeMillis());
                gbActivityOperateMapper.insert(operate);

//                // sku修改为不可用
//                GbActivityConfigSku configSku = new GbActivityConfigSku();
//                configSku.setStatus(GbActivityConfigSkuStatusEnum.REMOVE.getCode());
//                LambdaQueryWrapper<GbActivityConfigSku> where = new LambdaQueryWrapper<>();
//                where.eq(GbActivityConfigSku::getActivityId, activityId);
//                gbActivityConfigSkuMapper.update(configSku, where);

                break;
            case CLEARED:
                throw new RuntimeException("活动已结清，无法开启活动！");
            case EXPIRED:
                throw new RuntimeException("活动已过期，无法开启活动！");
            default:
                throw new RuntimeException("活动状态不合法！");
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void expiredActivity(Long activityId) {
        //  0. 锁活动，防止新团进入
        handleExpire(activityId);
        // 1. 判定该活动下是否还存在未结束的团员
        handleRelease(activityId);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteActivity(Long activityId) {
        GbActivityInfo gbActivityInfo = gbActivityInfoMapper.selectById(activityId);
        if(!gbActivityInfo.getActivityStatus().equals(GbActivityStatusEnum.NEW.getCode())){
            throw new RuntimeException("活动已开始或者执行过，无删除活动！");
        }
        LambdaQueryWrapper<GbActivityConfigSku> configSkuWrapper = new LambdaQueryWrapper<>();
        configSkuWrapper.eq(GbActivityConfigSku::getActivityId, activityId);
        List<GbActivityConfigSku> configSkuList = gbActivityConfigSkuMapper.selectList(configSkuWrapper);
        releaseConfigSkuInventory(activityId, configSkuList, "删除");

        gbActivityInfoMapper.deleteById(activityId);
        gbActivityConfigSkuMapper.delete(new LambdaQueryWrapper<GbActivityConfigSku>()
                .eq(GbActivityConfigSku::getActivityId, activityId));
        gbActivityConfigRewardMapper.delete(new LambdaQueryWrapper<GbActivityConfigReward>()
                .eq(GbActivityConfigReward::getActivityId, activityId));
        gbActivityConfigExtendMapper.delete(new LambdaQueryWrapper<GbActivityConfigExtend>()
                .eq(GbActivityConfigExtend::getActivityId, activityId));
        gbActivityOperateMapper.delete(new LambdaQueryWrapper<GbActivityOperate>()
                .eq(GbActivityOperate::getActivityId, activityId));

    }

    private void releaseConfigSkuInventory( Long activityId, List<GbActivityConfigSku> configSkuList, String actionDesc) {
        log.info("活动 {} 释放库存 {}", actionDesc, activityId);
        for (GbActivityConfigSku sku : configSkuList) {
            Long shopId = sku.getShopId();
            Long skuId = sku.getSkuId();
            log.info("活动 {} 释放库存 SKU {}", actionDesc, skuId);
            merchantCommodityInventoryGateway.release(shopId, activityId, skuId);
        }
    }

    public void handleExpire(Long activityId){
        // 1. 更新活动信息
        GbActivityInfo info = new GbActivityInfo();
        info.setId(activityId);
        info.setActivityStatus(GbActivityStatusEnum.EXPIRED.getCode());
        gbActivityInfoMapper.updateById(info);
        // 2. 添加活动记录
        GbActivityOperate operate = new GbActivityOperate();
        operate.setActivityId(info.getId());
        operate.setOperateType(GbActivityOperateTypeEnum.AUTO_INVALIDATION.getCode());
        operate.setOperateContent(GbActivityOperateTypeEnum.AUTO_INVALIDATION.getDescription());
        operate.setOperateUser(UserHelper.getUserId());
        operate.setOperateTime(System.currentTimeMillis());
        gbActivityOperateMapper.insert(operate);
    }

    public void handleRelease(Long activityId){
        GbGroupMemberQry memberQry = new GbGroupMemberQry();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        memberQry.setStatusList(statusList);
        Long count = gbGroupMemberGateway.count(memberQry);
        if(count == 0){
            LambdaQueryWrapper<GbActivityConfigSku> configSkuWrapper = new LambdaQueryWrapper<>();
            configSkuWrapper.eq(GbActivityConfigSku::getActivityId, activityId);
            List<GbActivityConfigSku> configSkuList = gbActivityConfigSkuMapper.selectList(configSkuWrapper);
            releaseConfigSkuInventory(activityId, configSkuList, "过期");
        }else {
            log.info("释放库存 存在团员数据 忽略 {} {}", activityId, count);
        }
    }
}
