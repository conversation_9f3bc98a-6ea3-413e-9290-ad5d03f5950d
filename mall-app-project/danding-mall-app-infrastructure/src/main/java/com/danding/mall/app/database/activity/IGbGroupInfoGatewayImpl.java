package com.danding.mall.app.database.activity;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.core.domain.base.BaseServiceImpl;
import com.danding.mall.app.activity.enums.*;
import com.danding.mall.app.base.session.helper.ShopHelper;
import com.danding.mall.app.base.session.helper.UserHelper;
import com.danding.mall.app.database.activity.entity.*;
import com.danding.mall.app.database.activity.mapper.*;
import com.danding.mall.app.domain.activity.gateway.IGbGroupInfoGateway;
import com.danding.mall.app.database.activity.util.GbGroupInfoUtil;
import com.danding.mall.app.activity.dto.GbGroupInfoQry;
import com.danding.mall.app.activity.dto.data.GbGroupInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 开团的团信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Component
public class IGbGroupInfoGatewayImpl extends BaseServiceImpl<
        GbGroupInfoMapper,
        GbGroupInfo,
        GbGroupInfoDTO,
        GbGroupInfoQry> implements IGbGroupInfoGateway {

    @Resource
    private GbGroupInfoMapper gbGroupInfoMapper;

    @Resource
    private GbGroupMemberMapper gbGroupMemberMapper;

    @Resource
    private GbGroupHiMapper gbGroupHiMapper;

    @Resource
    private GbActivityInfoMapper gbActivityInfoMapper;

    @Resource
    private GbGroupMembersRewardMapper gbGroupMembersRewardMapper;

    @Resource
    private GbActivityConfigRewardMapper gbActivityConfigRewardMapper;

    @Override
    public LambdaQueryWrapper<GbGroupInfo> getCommonWrapper(GbGroupInfoQry param) {
        return GbGroupInfoUtil.getQueryWrapper(param, baseWrapper(param));
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long leaderCreate(Long shopId, Long activityId, Long orderId) {
        //1. 添加团信息,附加团创建时间，团初始状态
        GbGroupInfo gbInfo = new GbGroupInfo();
        gbInfo.setActivityId(activityId);
        gbInfo.setGroupTime(System.currentTimeMillis());
        gbInfo.setGroupStatus(GbGroupStatusEnum.LEADER_PENDING_PAYMENT.getCode());
        gbInfo.setShopId(shopId);
        gbGroupInfoMapper.insert(gbInfo);
        //2. 添加团员信息,附加团id、用户id、团员角色、进团时间、团员状态
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setActivityId(activityId);
        gbMember.setGroupId(gbInfo.getId());
        gbMember.setGroupUserId(UserHelper.getUserId());
        gbMember.setMemberRole(GbGroupMemberRoleEnum.MEMBER_ROLE_LEADER.getCode());
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        gbMember.setOrderId(orderId);
        gbMember.setShopId(shopId);
        gbGroupMemberMapper.insert(gbMember);
        //3. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(gbInfo.getId());
        gbGroupHi.setActivityId(gbInfo.getActivityId());
        gbGroupHi.setOperateType(GbGroupOperateEnum.CREATE_GROUP.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.CREATE_GROUP.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHi.setShopId(shopId);
        gbGroupHiMapper.insert(gbGroupHi);
        return gbInfo.getId();
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderCancelPay(Long shopId, Long activityId, Long groupId, Long memberId) {
        //1. 更新团信息，更新团状态为：已取消
        GbGroupInfo gbInfo = new GbGroupInfo();
        gbInfo.setId(groupId);
        gbInfo.setGroupStatus(GbGroupStatusEnum.GROUP_FAILED.getCode());
        gbInfo.setFailReason(GbGroupFailReasonEnum.LEADER_OUT.getCode());
        gbGroupInfoMapper.updateById(gbInfo);
        //2. 更新团员信息,更新团状态为：退团,退园原因：团长取消支付
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setId(memberId);
        gbMember.setOutTime(System.currentTimeMillis());
        gbMember.setOutReason(GbGroupFailReasonEnum.LEADER_CANCEL_OUT.getDescription());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_OUT.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        //3. 添加团生命周期记录，操作类型：开团失败，操作内容：团长取消支付
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.GROUP_DISABLE.getCode());
        gbGroupHi.setOperateContent(GbGroupFailReasonEnum.LEADER_CANCEL_OUT.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaderPay(Long shopId, Long activityId, Long groupId, Long memberId) {
        //1. 更新团信息，更新团状态为：进行中
        GbGroupInfo gbInfo = new GbGroupInfo();
        gbInfo.setId(groupId);
        gbInfo.setGroupStatus(GbGroupStatusEnum.IN_PROGRESS.getCode());
        gbGroupInfoMapper.updateById(gbInfo);
        //2. 更新团员信息,更新团状态为：在团
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setId(memberId);
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        //3. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.GROUP_PAY.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.GROUP_PAY.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long memberIn(Long activityId,Long groupId, Long orderId) {
        Long shopId = ShopHelper.getId();
        //1. 添加普通团员信息
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setGroupId(groupId);
        gbMember.setActivityId(activityId);
        gbMember.setGroupUserId(UserHelper.getUserId());
        gbMember.setMemberRole(GbGroupMemberRoleEnum.MEMBER_ROLE_NORMAL.getCode());
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        gbMember.setOrderId(orderId);
        gbMember.setShopId(shopId);
        gbGroupMemberMapper.insert(gbMember);
        //2. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.MEMBER_JOIN_BEFORE_PAY.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.MEMBER_JOIN_BEFORE_PAY.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
        return gbMember.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberPay(Long shopId, Long activityId, Long groupId, Long memberId) {
        //1. 更新团员信息,更新团状态为：在团
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setId(memberId);
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        //2. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.MEMBER_JOIN_AFTER_PAY.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.MEMBER_JOIN_AFTER_PAY.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
        //3. 获取团员数量和成团成员数量进行比对，并自动成团
        Long count = gbGroupMemberMapper.selectCount(new LambdaQueryWrapper<GbGroupMember>()
                .eq(GbGroupMember::getGroupId, groupId)
                .eq(GbGroupMember::getMemberStatus, GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode()));
        GbActivityInfo gbActivityInfo = gbActivityInfoMapper.selectById(activityId);
        if(count.intValue() == gbActivityInfo.getActivityNum()){
            groupSuccess(shopId, activityId, groupId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberOut(Long shopId, Long activityId, Long groupId, Long memberId) {
        //1. 更新团员信息,更新团状态为：退团
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setId(memberId);
        gbMember.setOutTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_OUT.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        //2. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.MEMBER_OUT.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.MEMBER_OUT.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
        //3. 团员退团，满员回退到进行中，其他状态不变
        GbGroupInfo gbGroupInfo = gbGroupInfoMapper.selectById(groupId);
        if(gbGroupInfo.getGroupStatus().equals(GbGroupStatusEnum.GROUP_FULL.getCode())){
            GbGroupInfo gbInfo = new GbGroupInfo();
            gbInfo.setId(groupId);
            gbInfo.setGroupStatus(GbGroupStatusEnum.IN_PROGRESS.getCode());
            gbGroupInfoMapper.updateById(gbInfo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void groupSuccess(Long shopId, Long activityId, Long groupId) {
        //1. 批量更新团员信息,更新在团成员状态为：开团成功
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        gbGroupMemberMapper.update(gbMember,new LambdaQueryWrapper<GbGroupMember>()
                .eq(GbGroupMember::getGroupId, groupId)
                .eq(GbGroupMember::getMemberStatus, GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode()));
        //2. 更新团信息，更新团状态为：开团成功
        GbGroupInfo gbInfo = new GbGroupInfo();
        gbInfo.setId(groupId);
        gbInfo.setGroupStatus(GbGroupStatusEnum.GROUP_SUCCESS.getCode());
        gbInfo.setSuccessTime(System.currentTimeMillis());
        gbGroupInfoMapper.updateById(gbInfo);
        //3. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.GROUP_SUCCESS.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.GROUP_SUCCESS.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
        //4. 生成团员奖励记录
        createGroupMembersReward(shopId,activityId, groupId);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void groupFull(Long shopId, Long activityId, Long groupId) {
        //1. 更新团信息，更新团状态为：团满员
        GbGroupInfo gbInfo = new GbGroupInfo();
        gbInfo.setId(groupId);
        gbInfo.setGroupStatus(GbGroupStatusEnum.GROUP_FULL.getCode());
        gbGroupInfoMapper.updateById(gbInfo);
    }
    @Transactional(rollbackFor = Exception.class)
    public void createGroupMembersReward(Long shopId, Long activityId, Long groupId) {
        log.info("开始生成带奖励记录，团ID为：{}",groupId);
        //1. 获取活动奖励配置列表
        LambdaQueryWrapper<GbActivityConfigReward> wrapperReward = new LambdaQueryWrapper<>();
        wrapperReward.eq(GbActivityConfigReward::getActivityId, activityId);
        List<GbActivityConfigReward> gbActivityConfigRewards = gbActivityConfigRewardMapper.selectList(wrapperReward);
        //2. 获取拼图成功人员
        LambdaQueryWrapper<GbGroupMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GbGroupMember::getGroupId, groupId);
        wrapper.eq(GbGroupMember::getMemberStatus, GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        List<GbGroupMember> gbGroupMembers = gbGroupMemberMapper.selectList(wrapper);
        //3. 生成拼团奖励记录
        for(GbActivityConfigReward gbActivityConfigReward:gbActivityConfigRewards){
            for(GbGroupMember gbGroupMember: gbGroupMembers){
                if(gbActivityConfigReward.getRewardRange().equals(gbGroupMember.getMemberRole())){
                    GbGroupMembersReward gbGroupMembersReward = new GbGroupMembersReward();
                    gbGroupMembersReward.setGroupId(groupId);
                    gbGroupMembersReward.setActivityId(activityId);
                    gbGroupMembersReward.setMemberId(gbGroupMember.getId());
                    gbGroupMembersReward.setRewardStatus(GbRewardStatusEnum.PRE_GANT.getCode());
                    gbGroupMembersReward.setShopId(UserHelper.getUserId());
                    // 3.1 发放福豆
                    if(gbActivityConfigReward.getFudouFlag().equals(1)){
                        gbGroupMembersReward.setId(null);
                        gbGroupMembersReward.setRewardType(GbRewardTypeEnum.FUDOU.getCode());
                        gbGroupMembersReward.setRewardQuota(gbActivityConfigReward.getFudouNum());
                        gbGroupMembersReward.setShopId(gbGroupMember.getShopId());
                        gbGroupMembersRewardMapper.insert(gbGroupMembersReward);
                        log.info("生成奖励记录（福豆），奖励记录ID：{}",gbGroupMembersReward.getId());
                    }
                    //3.2 发放福卡
                    if(gbActivityConfigReward.getFukaFlag().equals(1)){
                        gbGroupMembersReward.setId(null);
                        gbGroupMembersReward.setRewardType(GbRewardTypeEnum.FUKA.getCode());
                        gbGroupMembersReward.setRewardQuota(gbActivityConfigReward.getFukaNum());
                        gbGroupMembersReward.setShopId(gbGroupMember.getShopId());
                        gbGroupMembersRewardMapper.insert(gbGroupMembersReward);
                        log.info("生成奖励记录（福卡），奖励记录ID：{}",gbGroupMembersReward.getId());
                    }
                    //3.3 发放优惠卷
                    if(gbActivityConfigReward.getCouponFlag().equals(1)){
                        String couponIds = gbActivityConfigReward.getCouponIds();
                        Arrays.stream(couponIds.split(";")).map(String::trim).forEach(couponId -> {
                            gbGroupMembersReward.setId(null);
                            gbGroupMembersReward.setRewardType(GbRewardTypeEnum.COUPON.getCode());
                            gbGroupMembersReward.setCouponId(Long.parseLong(couponId));
                            gbGroupMembersReward.setShopId(gbGroupMember.getShopId());
                            gbGroupMembersRewardMapper.insert(gbGroupMembersReward);
                            log.info("生成奖励记录（优惠卷），奖励记录ID：{}",gbGroupMembersReward.getId());
                        });
                    }
                }
            }
        }


    }

    @Override
    public void memberSigned(Long shopId, Long activityId, Long groupId, Long memberId) {
        //1. 更新团员信息：已签收
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setId(memberId);
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        log.info("团成员状态变更为已签收 {}", memberId);
    }

    @Override
    public void memberShipped(Long shopId, Long activityId, Long groupId, Long memberId) {
        //1. 更新团员信息：已发货
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setId(memberId);
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        log.info("团成员状态变更为已发货 {}", memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long memberOccupancy(Long activityId, Long groupId,Long shopId,Long userId) {
        log.info("新用户注册成功，用户ID：{}，开始参团： {}", userId,groupId);
        //0. 添加判斷若团已满无法进入
        GbActivityInfo gbActivityInfo = gbActivityInfoMapper.selectById(activityId);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SUCCESS.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_DELIVERED.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_SIGNED.getCode());
        Long count = gbGroupMemberMapper.selectCount(new LambdaQueryWrapper<GbGroupMember>()
                .eq(GbGroupMember::getGroupId, groupId)
                .in(GbGroupMember::getMemberStatus, statusList));
        if(count >= gbActivityInfo.getActivityNum()){
            log.info("团已满，无法参团");
            throw new RuntimeException("团已满，无法参团");
        }
        //1. 添加普通团员信息
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setGroupId(groupId);
        gbMember.setActivityId(activityId);
        gbMember.setGroupUserId(userId);
        gbMember.setMemberRole(GbGroupMemberRoleEnum.MEMBER_ROLE_NORMAL.getCode());
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
        gbMember.setShopId(shopId);
        gbGroupMemberMapper.insert(gbMember);
        //2. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.MEMBER_OCCUPANCY.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.MEMBER_OCCUPANCY.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
        //3. 获取团员数量和成团成员数量进行比对，自动满员
        if(count+1 >= gbActivityInfo.getActivityNum()){
            groupFull(shopId, activityId, groupId);
        }
        return gbMember.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long memberOInAfterOccupancy(Long shopId, Long activityId, Long groupId, Long memberId, Long orderId) {
        log.info("订单新增，并进行参团操作： {}", orderId);
        GbGroupMember gbMember = new GbGroupMember();
        if(memberId == null){
            GbGroupMember gbGroupMember = gbGroupMemberMapper.selectOne(new LambdaQueryWrapper<GbGroupMember>()
                    .eq(GbGroupMember::getMemberStatus, GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode())
                    .eq(GbGroupMember::getGroupUserId, UserHelper.getUserId())
                    .eq(GbGroupMember::getGroupId, groupId));
            gbMember.setId(gbGroupMember.getId());
        }else{
            gbMember.setId(memberId);
        }
        //1. 更新团员信息,更新团状态为：待支付
        gbMember.setJoinTime(System.currentTimeMillis());
        gbMember.setOrderId(orderId);
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        //2. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.MEMBER_JOIN_BEFORE_PAY.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.MEMBER_JOIN_BEFORE_PAY.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
        return orderId;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void LeaderRefund(Long shopId, Long activityId, Long groupId, Long memberId) {
        log.info("团长退款，团员ID为： {}", memberId);
        //1. 更新团信息，更新团状态为：开团失败
        GbGroupInfo gbInfo = new GbGroupInfo();
        gbInfo.setId(groupId);
        gbInfo.setGroupStatus(GbGroupStatusEnum.GROUP_FAILED.getCode());
        gbInfo.setFailReason(GbGroupFailReasonEnum.LEADER_OUT.getCode());
        gbGroupInfoMapper.updateById(gbInfo);
        //2. 批量更新团员信息,更新团状态为：开团失败,退园原因：团长退款
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_OUT.getCode());
        gbMember.setOutTime(System.currentTimeMillis());
        gbMember.setOutReason(GbGroupFailReasonEnum.LEADER_OUT.getDescription());
        List<Integer> statusList = new ArrayList<>();
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_IN.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_BEFORE_PAY.getCode());
        statusList.add(GbGroupMemberStatusEnum.MEMBER_STATUS_OCCUPANCY.getCode());
        gbGroupMemberMapper.update(gbMember,new LambdaQueryWrapper<GbGroupMember>()
                .eq(GbGroupMember::getGroupId, groupId)
                .eq(GbGroupMember::getId, memberId)
                .in(GbGroupMember::getMemberStatus, statusList));
        //3. 添加团生命周期记录，操作类型：开团失败，操作内容：团长退团
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.LEADER_OUT.getCode());
        gbGroupHi.setOperateContent(GbGroupFailReasonEnum.LEADER_OUT.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberRefund(Long shopId, Long activityId, Long groupId, Long memberId) {
        //1. 更新团员信息,更新团状态为：退团
        GbGroupMember gbMember = new GbGroupMember();
        gbMember.setId(memberId);
        gbMember.setOutTime(System.currentTimeMillis());
        gbMember.setOutReason(GbGroupMemberStatusEnum.MEMBER_STATUS_OUT.getDescription());
        gbMember.setMemberStatus(GbGroupMemberStatusEnum.MEMBER_STATUS_OUT.getCode());
        gbGroupMemberMapper.updateById(gbMember);
        //2. 添加团生命周期记录
        GbGroupHi gbGroupHi = new GbGroupHi();
        gbGroupHi.setGroupId(groupId);
        gbGroupHi.setActivityId(activityId);
        gbGroupHi.setOperateType(GbGroupOperateEnum.MEMBER_OUT.getCode());
        gbGroupHi.setOperateContent(GbGroupOperateEnum.MEMBER_OUT.getDescription());
        gbGroupHi.setOperateTime(System.currentTimeMillis());
        gbGroupHi.setShopId(shopId);
//        gbGroupHi.setOperateUser(Objects.requireNonNull(UserHelper.getUserId()).toString());
        gbGroupHiMapper.insert(gbGroupHi);
        //3. 团员退团，满员回退到进行中，其他状态不变
        GbGroupInfo gbGroupInfo = gbGroupInfoMapper.selectById(groupId);
        if(gbGroupInfo.getGroupStatus().equals(GbGroupStatusEnum.GROUP_FULL.getCode())){
            GbGroupInfo gbInfo = new GbGroupInfo();
            gbInfo.setId(groupId);
            gbInfo.setGroupStatus(GbGroupStatusEnum.IN_PROGRESS.getCode());
            gbGroupInfoMapper.updateById(gbInfo);
        }

    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearGroup(GbGroupInfoDTO gbGroup) {


    }



}
