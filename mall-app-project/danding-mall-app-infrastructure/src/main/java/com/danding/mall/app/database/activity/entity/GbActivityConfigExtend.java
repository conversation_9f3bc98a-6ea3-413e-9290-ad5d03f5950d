package com.danding.mall.app.database.activity.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.danding.core.domain.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* <p>
* 拼团活动动态配置
* </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="GbActivityConfigExtend对象", description="拼团活动动态配置")
@TableName("dt_gb_activity_config_extends")
public class GbActivityConfigExtend extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "参数code")
    private String paraCode;

    @ApiModelProperty(value = "参数名称")
    private String paraName;

    @ApiModelProperty(value = "参数值")
    private String paraValue;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "版本")
    @Version
    @TableField(fill = FieldFill.INSERT)
    protected Integer version;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Long createdTime;

    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @ApiModelProperty(value = "最后修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableLogic(value="0",delval="1")
    @ApiModelProperty(value = "是否已删除",required = false,hidden = true)
    protected Integer deleted;

    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    public void setId(Long id) {
        this.id = id;
    }
}
