package com.danding.mall.app.database.activity.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.danding.core.domain.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* <p>
* 拼团活动-奖励配置表
* </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="GbActivityConfigReward对象", description="拼团活动-奖励配置表")
@TableName("dt_gb_activity_config_reward")
public class GbActivityConfigReward extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "奖励范围：1：团长 2：团员")
    private Integer rewardRange;

    @ApiModelProperty(value = "是否奖励福豆")
    private Integer fudouFlag;

    @ApiModelProperty(value = "福豆奖励额度")
    private Double fudouNum;

    @ApiModelProperty(value = "福豆奖励时间（确认收货N小时后）")
    private Integer fudouDelayHour;

    @ApiModelProperty("福豆过期天数")
    private Integer fudouExpireHours;

    @ApiModelProperty(value = "是否奖励福卡")
    private Integer fukaFlag;

    @ApiModelProperty(value = "奖励福卡额度")
    private Double fukaNum;

    @ApiModelProperty(value = "福卡奖励时间（确认收货N小时后）")
    private Integer fukaDelayHour;

    @ApiModelProperty(value = "是否奖励优惠卷")
    private Integer couponFlag;

    @ApiModelProperty(value = "优惠卷ID集合")
    private String couponIds;

    @ApiModelProperty(value = "优惠卷奖励时间（确认收货N小时后）")
    private Integer couponDelayHour;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "版本")
    @Version
    @TableField(fill = FieldFill.INSERT)
    protected Integer version;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Long createdTime;

    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @ApiModelProperty(value = "最后修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableLogic(value="0",delval="1")
    @ApiModelProperty(value = "是否已删除",required = false,hidden = true)
    protected Integer deleted;

    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;
    public void setId(Long id) {
        this.id = id;
    }
}
