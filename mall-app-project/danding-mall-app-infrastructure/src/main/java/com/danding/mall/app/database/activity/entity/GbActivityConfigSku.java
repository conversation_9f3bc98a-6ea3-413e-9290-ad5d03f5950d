package com.danding.mall.app.database.activity.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.danding.core.domain.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* <p>
* 平团活动商品关联表
* </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="GbActivityConfigSpu对象", description="平团活动商品关联表")
@TableName("dt_gb_activity_config_sku")
public class GbActivityConfigSku extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "item_id")
    private Long itemId;

    @ApiModelProperty(value = "sku_id")
    private Long skuId;

    @ApiModelProperty(value = "拼团价")
    private Integer gbPrice;

    @ApiModelProperty(value = "限购数量")
    private Integer skuLimit;

    @ApiModelProperty(value = "可用福豆")
    private Integer isFudou;

    @ApiModelProperty(value = "可用礼金")
    private Integer isLijing;

    @ApiModelProperty(value = "可用优惠")
    private Integer isYouhui;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "版本")
    @Version
    @TableField(fill = FieldFill.INSERT)
    protected Integer version;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Long createdTime;

    @ApiModelProperty(value = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @ApiModelProperty(value = "最后修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @TableLogic(value="0",delval="1")
    @ApiModelProperty(value = "是否已删除",required = false,hidden = true)
    protected Integer deleted;

    @ApiModelProperty(value = "是否参与拼团")
    private Integer joinFlag;

    @ApiModelProperty(value = "活动库存")
    private Integer skuStock;

    @ApiModelProperty(value = "营销工具ID（活动类型）")
    private Integer marketingToolId;

    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    public void setId(Long id) {
        this.id = id;
    }
}
