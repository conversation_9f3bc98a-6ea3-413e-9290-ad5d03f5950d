package com.danding.mall.app.database.inventory;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.core.domain.base.BaseServiceImpl;
import com.danding.mall.app.database.inventory.lifecycle.DefauultActivityInventoryLifecycle;
import com.danding.mall.app.database.inventory.template.operation.impl.*;
import com.danding.mall.app.database.inventory.template.operation.refund.InTransitQtyRefundOperation;
import com.danding.mall.app.database.inventory.template.operation.refund.LockedStockRefundOperation;
import com.danding.mall.app.domain.inventory.gateway.IMerchantCommodityInventoryGateway;
import com.danding.mall.app.database.inventory.entity.MerchantCommodityInventory;
import com.danding.mall.app.database.inventory.mapper.MerchantCommodityInventoryMapper;
import com.danding.mall.app.database.inventory.util.MerchantCommodityInventoryUtil;
import com.danding.mall.app.inventory.dto.MerchantCommodityInventoryQry;
import com.danding.mall.app.inventory.dto.data.MerchantCommodityInventoryDTO;
import com.danding.mall.app.inventory.enums.*;
import com.danding.mall.app.order.dto.data.SkuOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 独立库存表	库存隔离表结构，用于后续通用库存功能 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Component
public class IMerchantCommodityInventoryGatewayImpl extends BaseServiceImpl<
        MerchantCommodityInventoryMapper,
        MerchantCommodityInventory,
        MerchantCommodityInventoryDTO,
        MerchantCommodityInventoryQry> implements IMerchantCommodityInventoryGateway {

    @Resource
    private DefauultActivityInventoryLifecycle defauultActivityInventoryLifecycle;

    // 减可用库存 + 加已分配库存
    @Resource
    protected AvailableToAllocatedOperation availableToAllocatedOperation;

    // 减已分配库存 + 加锁定库存
    @Resource
    protected AllocatedToLockedOperation allocatedToLockedOperation;

    // 减锁定库存 + 加在途库存
    @Resource
    protected LockedToInTransitOperation lockedToInTransitOperation;

    // 减分配库存 + 加可用库存
    @Resource
    protected AllocatedToAvailableOperation allocatedToAvailableOperation;

    // 减锁定库存 + 加分配库存
    @Resource
    protected LockedToAllocatedOperation lockedToAllocatedOperation;

    @Resource
    protected InTransitQtyRefundOperation inTransitQtyRefundOperation;

    @Resource
    protected LockedStockRefundOperation lockedStockRefundOperation;

    @Resource
    protected AvailableToLockedOperation availableToLockedOperation;

    @Resource
    protected LockedToAvailableOperation lockedToAvailableOperation;

    @Resource
    protected InTransitToAvailableOperation inTransitToAvailableOperation;

    @Override
    public LambdaQueryWrapper<MerchantCommodityInventory> getCommonWrapper(MerchantCommodityInventoryQry param) {
        return MerchantCommodityInventoryUtil.getQueryWrapper(param, baseWrapper(param));
    }

    @Override
    public void create(Long shopId, Long skuId, Long activityId, Integer initQty, InventoryTypeEnum stockType) {
        defauultActivityInventoryLifecycle.create(shopId, skuId, activityId, initQty, stockType);
    }

    @Override
    public void release(Long shopId, Long inventorId) {
        defauultActivityInventoryLifecycle.release(shopId, inventorId);
    }

    @Override
    public void release(Long shopId, Long activityId, Long skuId) {
        defauultActivityInventoryLifecycle.release(shopId, activityId, skuId);
    }

    @Override
    public void releaseByActivityId(Long shopId, Long activityId) {
        Assert.notNull(shopId, "shopId 不能为空");
        Assert.notNull(activityId, "activityId 不能为空");

        MerchantCommodityInventoryQry inventoryQry = new MerchantCommodityInventoryQry();
        inventoryQry.setActivityId(activityId);
        List<MerchantCommodityInventoryDTO> list = selectList(inventoryQry);
        for (MerchantCommodityInventoryDTO dto : list) {
            defauultActivityInventoryLifecycle.release(shopId, dto.getId());
        }
    }

    @Override
    public void availableToAllocatedOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        availableToAllocatedOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);

    }

    @Override
    public void allocatedToLockedOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        allocatedToLockedOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);

    }

    @Override
    public void lockedToInTransitOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        lockedToInTransitOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);

    }

    @Override
    public void lockedToAllocatedOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        lockedToAllocatedOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
    }

    @Override
    public void allocatedToAvailableOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        allocatedToAvailableOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
    }

    @Override
    public void inTransitQtyRefundOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        inTransitQtyRefundOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
    }

    @Override
    public void lockedStockRefundOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        lockedStockRefundOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
    }

//    @Override
//    public void availableToAllocatedOperation(Long shopId, int quantity, Long orderId, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
//    }
//
//    @Override
//    public void allocatedToLockedOperation(Long shopId, int quantity, Long orderId, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
//    }
//
//    @Override
//    public void lockedToInTransitOperation(Long shopId, int quantity, Long orderId, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
//    }
//
//    @Override
//    public void lockedToAllocatedOperation(Long shopId, int quantity, Long orderId, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
//    }
//
//    @Override
//    public void allocatedToAvailableOperation(Long shopId, Long inventoryId, int quantity, Long orderId, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
//    }

    @Override
    public MerchantCommodityInventoryDTO findByActivityIdAndSkuId(Long activityId, Long skuId) {
        MerchantCommodityInventoryQry inventoryQry = new MerchantCommodityInventoryQry();
        inventoryQry.setActivityId(activityId);
        inventoryQry.setSkuId(skuId);
        inventoryQry.setStatus(InventoryStateEnum.ENABLED.getCode());
        return selectOne(inventoryQry);
    }

    @Override
    public void availableToLockedOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        availableToLockedOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
    }

    @Override
    public void lockedToAvailableOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        lockedToAvailableOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
    }

    @Override
    public void inTransitToAvailableOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource) {
        inTransitToAvailableOperation.execute(shopId, skuOrder, inventoryChangeType, inventoryChangeSource);
    }
}
