package com.danding.mall.app.database.inventory.template.operation;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.core.redis.lock.RedissonDistributedLock;
import com.danding.mall.app.base.constants.RocketMQConstant;
import com.danding.mall.app.database.inventory.entity.MerchantCommodityInventory;
import com.danding.mall.app.database.inventory.entity.MerchantInventoryTransaction;
import com.danding.mall.app.database.inventory.mapper.MerchantCommodityInventoryMapper;
import com.danding.mall.app.database.inventory.mapper.MerchantInventoryTransactionMapper;
import com.danding.mall.app.database.order.mapper.ShopOrderMapper;
import com.danding.mall.app.inventory.enums.AdjustStoreFieldEnum;
import com.danding.mall.app.inventory.enums.BasicInOutType;
import com.danding.mall.app.inventory.enums.InventoryChangeSource;
import com.danding.mall.app.inventory.enums.InventoryChangeType;
import com.danding.mall.app.order.dto.data.SkuOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 库存操作抽象类：固化了操作流程
 */
@Slf4j
public abstract class AbsInventoryOperation implements InventoryOperation {
    @Resource
    protected MerchantInventoryTransactionMapper merchantInventoryTransactionMapper;

    @Resource
    protected MerchantCommodityInventoryMapper merchantCommodityInventoryMapper;

    @Resource
    protected RedissonDistributedLock distributedLock;

    @Resource
    private SpringRocketMQProducer springRocketMQProducer;

    @Resource
    protected ShopOrderMapper shopOrderMapper;

    /**
     * 库存操作事务入口
     *  ps：调用入口放在每个实现类里，不然业务入口定位类文件太麻烦了
     *  TODO：此处采用新开事务，保证先有锁、后有事务。 也就意味着库存操作是最小操作单元。
     *  如果要使用外部业务的事务时、怎么保证事务与锁的问题？
     * @param shopId
     * @param inventoryChangeType
     * @param inventoryChangeSource
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void executeOperation(Long shopId, SkuOrderDTO skuOrder, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource){
        Integer quantity = Math.toIntExact(skuOrder.getQuantity());
        Long skuOrderId = skuOrder.getId();

        MerchantCommodityInventory dbDTO = findInventoryRecord(shopId, skuOrder);

        this.validateInventory(quantity, dbDTO);

        this.validateChangeRecord(shopId, skuOrderId, dbDTO.getId(), inventoryChangeType);

        this.updateInventory(quantity, dbDTO, skuOrderId, inventoryChangeType, inventoryChangeSource);
    }

    /**
     * @param inventoryEntity
     * @param inventoryChangeType
     * @param inventoryChangeSource
     * @param shopOrderId
     * @param quantity
     * @param dbQty
     * @param newQty
     * @param adjustStoreFieldEnum
     */
    protected void recordTransaction(MerchantCommodityInventory inventoryEntity,
                                     InventoryChangeType inventoryChangeType,
                                     InventoryChangeSource inventoryChangeSource,
                                     Long shopOrderId,
                                     int quantity,
                                     int dbQty,
                                     int newQty,
                                     AdjustStoreFieldEnum adjustStoreFieldEnum,
                                     BasicInOutType basicInOutType) {

        Date adjustTime = new Date();

        MerchantInventoryTransaction transactionDTO = new MerchantInventoryTransaction();
        transactionDTO.setShopId(inventoryEntity.getShopId());
        transactionDTO.setStoreId(inventoryEntity.getId());

        // 具体某类库存的变更详情
        transactionDTO.setBeforeStock(dbQty);
        transactionDTO.setAfterStock(newQty);
        transactionDTO.setAdjustStock(quantity);
        transactionDTO.setAdjustStoreField(adjustStoreFieldEnum.getCode());
        transactionDTO.setAdjustStoreFieldDesc(adjustStoreFieldEnum.getDescription());
        // 加减类型
        transactionDTO.setInOut(basicInOutType.getCode());

        transactionDTO.setAdjustType(inventoryChangeType.getCode());
        transactionDTO.setAdjustSource(inventoryChangeSource.getCode());
        transactionDTO.setAdjustUser("system");
        transactionDTO.setAdjustTime(adjustTime);
        transactionDTO.setReferenceNo(shopOrderId);
        transactionDTO.setRemarks("");
        merchantInventoryTransactionMapper.insert(transactionDTO);

    }
    /**
     * 同一个订单 + 某个独立库存 + 某个订单事件{@link InventoryChangeType} : 只能处理一次
     * @param shopId
     * @param orderId
     * @param inventoryId
     * @param inventoryChangeType
     */
    private void validateChangeRecord(Long shopId, Long orderId, Long inventoryId, InventoryChangeType inventoryChangeType) {
//        MerchantInventoryTransactionQry transactionQry = new MerchantInventoryTransactionQry();
//        transactionQry.setShopId(shopId);
//        transactionQry.setStoreId(inventoryId);
//        transactionQry.setAdjustType(inventoryChangeType.getCode());
//        transactionQry.setReferenceNo(orderId);

        LambdaUpdateWrapper<MerchantInventoryTransaction> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MerchantInventoryTransaction::getId, inventoryId);
        wrapper.eq(MerchantInventoryTransaction::getStoreId, inventoryId);
        wrapper.eq(MerchantInventoryTransaction::getAdjustType, inventoryChangeType.getCode());
        wrapper.eq(MerchantInventoryTransaction::getReferenceNo, orderId);
        List<MerchantInventoryTransaction> transactionDTOList = merchantInventoryTransactionMapper.selectList(wrapper);
        // 一个事件可能产生多个记录
        if(CollectionUtil.isEmpty(transactionDTOList)){
            return;
        }
        log.error("该事件已经发起过库存操作");
        throw new RuntimeException("该事件已经发起过库存操作");
    }

    /**
     * 库存校验
     *
     * @param quantity
     * @param entity
     */
    protected abstract void validateInventory(Integer quantity, MerchantCommodityInventory entity);

    /**
     * 修改库存后的动作
     *
     * @param quantity              要变更的库存数量
     * @param entity                原数据库库存信息
     * @param orderId               订单ID
     * @param inventoryChangeType   变更类型
     * @param inventoryChangeSource 操作来源
     */
    protected abstract void updateInventory(int quantity, MerchantCommodityInventory entity, Long orderId, InventoryChangeType inventoryChangeType, InventoryChangeSource inventoryChangeSource);

    protected MerchantCommodityInventory findInventoryRecord(Long shopId, SkuOrderDTO skuOrder) {
//        MerchantCommodityInventoryQry merchantCommodityInventoryQry = new MerchantCommodityInventoryQry();
//        merchantCommodityInventoryQry.setId(inventoryId);
//        merchantCommodityInventoryQry.setShopId(shopId);

//        LambdaUpdateWrapper<ShopOrder> skuOrderWrapper = new LambdaUpdateWrapper<>();
//        skuOrderWrapper.eq(ShopOrder::getShopId, shopId);
//        skuOrderWrapper.eq(ShopOrder::getId, shopOrderId);
//        ShopOrder shopOrder = shopOrderMapper.selectOne(skuOrderWrapper);
//        if(shopOrder == null){
//            throw new RuntimeException("订单记录不存在 " + shopOrderId);
//        }

        LambdaUpdateWrapper<MerchantCommodityInventory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MerchantCommodityInventory::getId, skuOrder.getInventoryId());
        wrapper.eq(MerchantCommodityInventory::getShopId, shopId);
        MerchantCommodityInventory dbDTO = merchantCommodityInventoryMapper.selectOne(wrapper);
        if(dbDTO == null){
            throw new RuntimeException("库存记录不存在 " + skuOrder.getInventoryId());
        }
        return dbDTO;
    }

    /**
     * 检查可用库存是否
     * @param skuOrder
     * @param inventoryId
     * @return
     */
    protected void isStockAvailable(SkuOrderDTO skuOrder, Long inventoryId) {
        MerchantCommodityInventory entity = merchantCommodityInventoryMapper.selectById(inventoryId);
        Integer dbAvailableQty = entity.getAvailableQty();
        Integer dbAllocatedQty = entity.getAllocatedQty();
        Integer dbLockedQty = entity.getLockedQty();

        boolean isAvailableStockUnusable = dbAvailableQty <= 0;
        boolean isAllocatedStockUnusable = dbAllocatedQty <= 0;
        boolean isLockedStockUnusable = dbLockedQty <= 0;
        log.info("检查库存是否不可用 {} 可用:{} 已分配:{} 已锁定:{}", entity.getId(), dbAvailableQty, dbAllocatedQty, dbLockedQty);

        if(isAvailableStockUnusable && isAllocatedStockUnusable && isLockedStockUnusable){
            log.info("独立库存 可用库存不足 {} {}", entity.getId());
            List<Long> skuIdList = new ArrayList<>();
            skuIdList.add(skuOrder.getSkuId());
            SpringMessage<List<Long>> springMessage = new SpringMessage<>(skuIdList);

            String topicAndTag = RocketMQConstant.OLD_APP_ADMIN_TOPIC + ":" + RocketMQConstant.SKU_STOCK_CHANGE_TAG;
            SendResult sendResult = springRocketMQProducer.syncSend(topicAndTag, springMessage);
            if (SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                log.info("独立库存不足 发送商品售罄任务消息成功 {}", sendResult.getMsgId());
            }else{
                log.info("独立库存不足 发送商品售罄任务消息成功");
            }
        }
    }
}
