package com.danding.mall.app.database.items;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.core.domain.CommonFieldsInProject;
import com.danding.core.domain.base.BaseServiceImpl;
import com.danding.core.domain.utils.ConverterUtil;
import com.danding.mall.app.database.items.entity.IntermediateInfo;
import com.danding.mall.app.database.items.mapper.IntermediateInfoMapper;
import com.danding.mall.app.database.items.mapper.ItemsMapper;
import com.danding.mall.app.database.items.util.IntermediateInfoUtil;
import com.danding.mall.app.domain.items.gateway.IIntermediateInfoGateway;
import com.danding.mall.app.domain.sku.gateway.ISkusGateway;
import com.danding.mall.app.items.dto.IntermediateInfoQry;
import com.danding.mall.app.items.dto.data.IntermediateInfoDTO;
import com.danding.mall.app.items.enums.CashGiftUnitEnum;
import com.danding.mall.app.items.enums.IntermediateInfoMatchingTypeEnum;
import com.danding.mall.app.items.enums.ThirdIntermediateType;
import com.danding.mall.app.sku.dto.data.SkusDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 拓传参数中间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Slf4j
@Component
public class IIntermediateInfoGatewayImpl extends BaseServiceImpl<
        IntermediateInfoMapper,
        IntermediateInfo,
        IntermediateInfoDTO,
        IntermediateInfoQry> implements IIntermediateInfoGateway {

    @Value("${beanConvertRate:0.1}")
    private BigDecimal beanConvertRate;

    @Resource
    private ISkusGateway skusGateway;

    public IIntermediateInfoGatewayImpl(){
        super.commonFieldsInProject = new CommonFieldsInProject() {
            @Override
            public List<String> fieldList() {
                List<String> list = new ArrayList<>();
                list.add("createdAt");
                list.add("updatedAt");
                return list;
            }
        };
    }

    @Resource
    private ItemsMapper itemsMapper;

    @Override
    public LambdaQueryWrapper<IntermediateInfo> getCommonWrapper(IntermediateInfoQry param) {
        return IntermediateInfoUtil.getQueryWrapper(param, baseWrapper(param));
    }

    @Override
    public IntermediateInfoDTO selectByItemId(Long itemId) {
        IntermediateInfoQry param = new IntermediateInfoQry();
        param.setThirdId(itemId);
        param.setType(ThirdIntermediateType.ITEM.getValue());
        param.setMatchingType(1);
        IntermediateInfo intermediateInfo = baseMapper.selectOne(getCommonWrapper(param));
        return BeanUtil.copyProperties(intermediateInfo, IntermediateInfoDTO.class);
    }

    @Override
    public BigDecimal getMaxDiscountAmountBySkuId(Long skuId, BigDecimal originalPrice) {
        IntermediateInfoDTO intermediateInfoDTO = this.selectOne(new IntermediateInfoQry().setThirdId(skuId).setType(ThirdIntermediateType.SKU.getValue()).setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode()));
        if (intermediateInfoDTO == null || Objects.equals(intermediateInfoDTO.getIsCashGift(), 0)) {
            return BigDecimal.ZERO;
        }
        // 是否超出福豆抵扣时间
        Date now = new Date();
        if (intermediateInfoDTO.getCashGiftUseTimeStart() != null) {
            if (DateUtil.compare(new Date(intermediateInfoDTO.getCashGiftUseTimeStart()), now) > 0) {
                return BigDecimal.ZERO;
            }
        }
        if(intermediateInfoDTO.getCashGiftUseTimeEnd() != null){
            if (DateUtil.compare(new Date(intermediateInfoDTO.getCashGiftUseTimeEnd()), now) < 0) {
                return BigDecimal.ZERO;
            }
        }
        Integer unit = intermediateInfoDTO.getUnit();
        BigDecimal maxDeduction = intermediateInfoDTO.getMaxDeduction();
        if (maxDeduction == null) {
            log.warn("福豆最抵扣金额未配置 skuId={}  view cart", skuId);
            return BigDecimal.ZERO;
        }
        if (maxDeduction.compareTo(new BigDecimal("0")) < 0) {
            return BigDecimal.ZERO;
        }
        SkusDTO skusDTO = skusGateway.getById(skuId);
        if (skusDTO == null) {
            log.warn("商品sku未获取 skuId {}", skuId);
            return BigDecimal.ZERO;
        }
        // 活动价
        skusGateway.processSkuForActivity(Lists.newArrayList(skusDTO));
        if (Objects.equals(unit, CashGiftUnitEnum.PERCENT.getCode())) {
            // 百分比存储，除100 x 商品价格
//            BigDecimal skuPrice = new BigDecimal(String.valueOf(skusDTO.getPrice()))
//                    .divide(new BigDecimal("100"));
//            BigDecimal skuPriceSum = skuPrice.multiply(maxDeduction.divide(new BigDecimal("100"))).setScale(2, RoundingMode.DOWN);
            return originalPrice.multiply(maxDeduction.divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
        } else if (Objects.equals(unit, CashGiftUnitEnum.YUAN.getCode())) {
            maxDeduction = maxDeduction.multiply(beanConvertRate);//福豆按照比例转换成金额
            return maxDeduction.setScale(2, RoundingMode.DOWN);
        } else {
            log.warn("福豆单位未配置 skuId {}", skuId);
            return BigDecimal.ZERO;
        }
    }


    @Override
    public List<IntermediateInfoDTO> findByThirdAndType(Long thirdId, Integer type) {
        return findByThirdAndType(thirdId, type, null);
    }

    /**
     *
     <select id="findByThirdAndType" parameterType="map" resultMap="IntermediateInfoMap">
     SELECT
     <include refid="cols_all"/>
     FROM
     <include refid="tb"/>
     WHERE
     third_id=#{thirdId}
     and `type`=#{type}
     <if test="matchingType == null">
     and (matching_type is null or matching_type = 1)
     </if>
     <if test="matchingType != null">
     and matching_type = #{matchingType}
     </if>
     </select>
     * @param thirdId
     * @param type
     * @return
     */
    @Override
    public List<IntermediateInfoDTO> findByThirdAndType(Long thirdId, Integer type, Integer matchingType) {
        Assert.notNull(thirdId, "thirdId 不能为空");
        Assert.notNull(type, "type 不能为空");

        LambdaQueryWrapper<IntermediateInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntermediateInfo::getThirdId, thirdId);
        wrapper.eq(IntermediateInfo::getType, type);
        if(matchingType == null){
            wrapper.and(w ->{
                w.isNull(IntermediateInfo::getMatchingType);
                w.eq(IntermediateInfo::getMatchingType, 1);
            });
        }else{
            wrapper.eq(IntermediateInfo::getMatchingType, matchingType);
        }
        List<IntermediateInfo> intermediateInfoList = this.baseMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(intermediateInfoList)){
            return new ArrayList<>();
        }
        return ConverterUtil.convertList(intermediateInfoList, IntermediateInfoDTO.class);
    }

    /**
     *
         delete from <include refid="tb"/>
         where `third_id` = #{thirdId}
         and `type` = #{type}
         and `matching_type` = #{matchingType}
     * @param thirdId
     * @param type
     * @param matchingTypeEnum
     */
    @Override
    public void delete(Long thirdId, ThirdIntermediateType type, IntermediateInfoMatchingTypeEnum matchingTypeEnum) {
        Assert.notNull(thirdId, "thirdId 不能为空");
        Assert.notNull(type, "type 不能为空");
        Assert.notNull(matchingTypeEnum, "matchingTypeEnum 不能为空");

        IntermediateInfoQry intermediateInfoQry = new IntermediateInfoQry();
        intermediateInfoQry.setThirdId(thirdId);
        intermediateInfoQry.setType(type.getValue());
        intermediateInfoQry.setMatchingType(matchingTypeEnum.getCode());
        this.delete(intermediateInfoQry);
    }

    /**
     * SELECT
     *         <include refid="cols_all"/>
     *         FROM
     *         <include refid="tb"/>
     *         WHERE
     *         third_id=#{thirdId}
     *         and `type`=#{type}
     * @param thirdId
     * @param type
     * @return
     */
    @Override
    public List<IntermediateInfoDTO> findAllByThirdAndType(Long thirdId, Integer type) {
        Assert.notNull(thirdId, "thirdId 不能为空");
        Assert.notNull(type, "type 不能为空");

        IntermediateInfoQry intermediateInfoQry = new IntermediateInfoQry();
        intermediateInfoQry.setThirdId(thirdId);
        intermediateInfoQry.setType(type);
        return selectList(intermediateInfoQry);
    }

    @Override
    public void insertOrUpdate(IntermediateInfoDTO intermediateInfo) {
        Long thirdId = intermediateInfo.getThirdId();
        Integer type = intermediateInfo.getType();
        Integer matchingType = intermediateInfo.getMatchingType();
        IntermediateInfoDTO dbData = this.selectOne(new IntermediateInfoQry().setThirdId(thirdId).setType(type).setMatchingType(matchingType));
        if (ObjUtil.isNotEmpty(dbData)) {
            intermediateInfo.setId(dbData.getId());
            this.update(intermediateInfo);
        } else {
            this.insert(intermediateInfo);
        }
    }
}
