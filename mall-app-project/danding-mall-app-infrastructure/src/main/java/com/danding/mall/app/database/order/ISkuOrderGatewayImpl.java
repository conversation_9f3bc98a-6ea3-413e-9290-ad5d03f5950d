package com.danding.mall.app.database.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.core.domain.base.BaseServiceImpl;
import com.danding.core.tool.utils.BeanUtil;
import com.danding.mall.app.database.order.entity.ShopOrder;
import com.danding.mall.app.domain.order.gateway.ISkuOrderGateway;
import com.danding.mall.app.database.order.entity.SkuOrder;
import com.danding.mall.app.database.order.mapper.SkuOrderMapper;
import com.danding.mall.app.database.order.util.SkuOrderUtil;
import com.danding.mall.app.order.dto.SkuOrderQry;
import com.danding.mall.app.order.dto.data.ShopOrderDTO;
import com.danding.mall.app.order.dto.data.SkuOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * sku维度订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Slf4j
@Component
public class ISkuOrderGatewayImpl extends BaseServiceImpl<
        SkuOrderMapper,
        SkuOrder,
        SkuOrderDTO,
        SkuOrderQry> implements ISkuOrderGateway {

    @Override
    public LambdaQueryWrapper<SkuOrder> getCommonWrapper(SkuOrderQry param) {
        return SkuOrderUtil.getQueryWrapper(param, baseWrapper(param));
    }

    @Override
    public List<SkuOrderDTO> selectByItemId(Long itemId) {
        SkuOrderQry orderQry = new SkuOrderQry();
        orderQry.setItemId(itemId);
        LambdaQueryWrapper<SkuOrder> queryWrapper = SkuOrderUtil.getQueryWrapper(orderQry, baseWrapper(orderQry));
        queryWrapper.notIn(SkuOrder::getStatus, 0, -1, -2, -13);
        queryWrapper.groupBy(SkuOrder::getOrderId);
        queryWrapper.orderByDesc(SkuOrder::getCreatedAt);
        queryWrapper.last("LIMIT 30");
        List<SkuOrder> list = this.list(queryWrapper);
        return list.stream().map(skuOrder -> BeanUtil.copy(skuOrder, SkuOrderDTO.class)).collect(Collectors.toList());
    }

    @Override
    public Long countBySkuIdAndOrderIds(Long shopId, Long skuId, List<Long> orderIds) {
        LambdaQueryWrapper<SkuOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SkuOrder::getShopId, shopId);
        wrapper.eq(SkuOrder::getSkuId, skuId);
        wrapper.in(SkuOrder::getOrderId, orderIds);
        return this.baseMapper.selectCount(wrapper);
    }

    @Override
    public List<SkuOrderDTO> findByShopOrderId(Long orderId) {
        SkuOrderQry orderQry = new SkuOrderQry();
        orderQry.setOrderId(orderId);
        return selectList(orderQry);
    }

    @Override
    public List<SkuOrderDTO> selectByOrderId(List<Long> orderIdList) {
        if (CollUtil.isEmpty(orderIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SkuOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SkuOrder::getOrderId, orderIdList);
        List<SkuOrder> skuOrders = this.getBaseMapper().selectList(wrapper);
        return this.convertToDTOList(skuOrders);
    }

    private List<SkuOrderDTO> convertToDTOList(List<SkuOrder> skuOrderDTOList) {
        if (CollUtil.isEmpty(skuOrderDTOList)) {
            return Collections.emptyList();
        }
        return skuOrderDTOList.stream().map(skuOrder -> {
            SkuOrderDTO skuOrderDTO = new SkuOrderDTO();
            BeanUtils.copyProperties(skuOrder, skuOrderDTO);
            return skuOrderDTO;
        }).collect(Collectors.toList());
    }

}
