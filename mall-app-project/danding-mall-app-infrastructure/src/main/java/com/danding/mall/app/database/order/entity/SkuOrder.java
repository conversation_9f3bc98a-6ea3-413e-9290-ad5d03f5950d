package com.danding.mall.app.database.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.mall.app.base.parana.ParanaEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
* <p>
* sku维度订单
* </p>
*
* <AUTHOR>
* @since 2025-03-07
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SkuOrder对象", description="sku维度订单")
@TableName("parana_sku_orders")
public class SkuOrder extends ParanaEntity {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    @ApiModelProperty(value = "sku id")
    private Long skuId;

    @ApiModelProperty(value = "sku版本号")
    private Integer skuVersion;

    @ApiModelProperty(value = "sku数量")
    private Long quantity;

    @ApiModelProperty(value = "实付金额")
    private Long fee;

    @ApiModelProperty(value = "子订单状态")
    private Integer status;

    @ApiModelProperty(value = "是否保税(1:保税，0:完税)")
    private Integer isBonded;

    @ApiModelProperty(value = "税费")
    private Long tax;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "买家id")
    private Long buyerId;

    @ApiModelProperty(value = "外部自订单id")
    private String outId;

    @ApiModelProperty(value = "买家名称")
    private String buyerName;

    @ApiModelProperty(value = "买家外部id")
    private String outBuyerId;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "是否为第三方商品（0：为自建商品，1：第三方商品）")
    private Integer isThirdPartyItem;

    @ApiModelProperty(value = "sku主图")
    private String skuImage;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "店铺外部id")
    private String outShopId;

    @ApiModelProperty(value = "外部订单来源")
    private String outFrom;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "外部sku编号")
    private String outerSkuId;

    @ApiModelProperty(value = "推送状态(0:不需要推送，1：待推送，2：完成推送)")
    private Integer pushStatus;

    @ApiModelProperty(value = "推单失败原因")
    private String pushErrorMsg;

    @ApiModelProperty(value = "推送时间")
    private Date pushTime;

    @ApiModelProperty(value = "sku属性, json表示")
    private String skuAttributes;

    @ApiModelProperty(value = "订单渠道 1-pc, 2-手机")
    private Integer channel;

    @ApiModelProperty(value = "支付类型 1-在线支付 2-货到付款")
    private Integer payType;

    @ApiModelProperty(value = "配送方式")
    private Integer shipmentType;

    @ApiModelProperty(value = "原价")
    private Long originFee;

    @ApiModelProperty(value = "优惠后费用（不含税费）")
    private Long afterDiscountFee;

    @ApiModelProperty(value = "")
    private Long discount;

    @ApiModelProperty(value = "运费")
    private Long shipFee;

    @ApiModelProperty(value = "运费折扣")
    private Long shipFeeDiscount;

    @ApiModelProperty(value = "积分减免金额")
    private Integer integral;

    @ApiModelProperty(value = "余额减免金额")
    private Integer balance;

    @ApiModelProperty(value = "单品级别的优惠id")
    private Long promotionId;

    @ApiModelProperty(value = "商品快照id")
    private Long itemSnapshotId;

    @ApiModelProperty(value = "是否申请过退款")
    private Integer hasRefund;

    @ApiModelProperty(value = "是否已开具过发票")
    private Integer invoiced;

    @ApiModelProperty(value = "是否已评价")
    private Integer commented;

    @ApiModelProperty(value = "是否申请过售后")
    private Integer hasApplyAfterSale;

    @ApiModelProperty(value = "电商平台佣金费率, 万分之一")
    private Integer commissionRate;

    @ApiModelProperty(value = "分销抽佣费率, 万分之一")
    private Integer distributionRate;

    @ApiModelProperty(value = "改价金额")
    private Integer diffFee;

    @ApiModelProperty(value = "子订单额外信息,json表示")
    private String extraJson;

    @ApiModelProperty(value = "子订单tag信息, json表示")
    private String tagsJson;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "")
    private Date shipmentAt;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    @ApiModelProperty(value = "")
    private String depotName;

    @ApiModelProperty(value = "")
    private String supplierName;

    @ApiModelProperty(value = "")
    private Long gatherOrderId;

    @ApiModelProperty(value = "")
    private String depotCode;

    @ApiModelProperty(value = "")
    private Long depotId;

    @ApiModelProperty(value = "")
    private Long feeId;

    @ApiModelProperty(value = "")
    private Long extraId;

    @ApiModelProperty(value = "")
    private Long profitId;

    @ApiModelProperty(value = "")
    private Long outFromType;

    @ApiModelProperty(value = "")
    private Long skuImageId;

    @ApiModelProperty(value = "订单上的各种标记(二进制位)")
    private Integer flag;

    @ApiModelProperty(value = "")
    private String categoryNameSnapshot;

    @ApiModelProperty(value = "发货仓类型(1:代塔仓自有,2：京东云交易)")
    private Integer shippingWarehouseType;

    @ApiModelProperty(value = "是否发货")
    private Boolean shipping;

    @ApiModelProperty(value = "金额明细json")
    private String discountDetailJson;

    @ApiModelProperty(value = "独立库存ID")
    private Long inventoryId;

    public void setId(Long id) {
        this.id = id;
    }
}
