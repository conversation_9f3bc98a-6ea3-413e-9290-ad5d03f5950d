package com.danding.mall.app.database.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.core.domain.base.BaseServiceImpl;
import com.danding.core.domain.utils.ConverterUtil;
import com.danding.mall.app.base.utils.RandomUtils;
import com.danding.mall.app.domain.user.gateway.IFamilyUserGateway;
import com.danding.mall.app.database.user.entity.FamilyUser;
import com.danding.mall.app.database.user.mapper.FamilyUserMapper;
import com.danding.mall.app.database.user.util.FamilyUserUtil;
import com.danding.mall.app.user.dto.FamilyUserQry;
import com.danding.mall.app.user.dto.data.FamilyUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Slf4j
@Component
public class IFamilyUserGatewayImpl extends BaseServiceImpl<
        FamilyUserMapper,
        FamilyUser,
        FamilyUserDTO,
        FamilyUserQry> implements IFamilyUserGateway {

    @Override
    public LambdaQueryWrapper<FamilyUser> getCommonWrapper(FamilyUserQry param) {
        return FamilyUserUtil.getQueryWrapper(param, baseWrapper(param));
    }

    @Override
    public FamilyUserDTO selectFamilyUser(Long shopId, Long userId) {
        FamilyUserQry familyUserQry = new FamilyUserQry();
        familyUserQry.setShopId(shopId);
        familyUserQry.setUserId(userId);
        return this.selectOne(familyUserQry);
    }

    @Override
    public FamilyUserDTO selectFamilyUser(Long shopId, String phoneOrUserId)  {
        FamilyUserQry familyUserQry = new FamilyUserQry();
        familyUserQry.setShopId(shopId);
        familyUserQry.setPhoneOrUserId(phoneOrUserId);

        FamilyUserDTO familyUserDTO = this.selectOne(familyUserQry);
        if(familyUserDTO == null){
            return null;
        }
        return familyUserDTO;
    }

    @Override
    public Long selectLastLoginTime(Long shopId, Long userId) {
        FamilyUserDTO dto = selectFamilyUser(shopId, userId);
        if(dto == null){
            return null;
        }
        return dto.getLastLoginTime();
    }

    @Override
    public Long selectFirstJoinGroupTime(Long shopId, Long userId) {
        FamilyUserDTO dto = selectFamilyUser(shopId, userId);
        if(dto == null){
            return null;
        }
        return dto.getFirstJoinGroupTime();
    }

    @Override
    public FamilyUserDTO selectByInviteCode(Long shopId, String inviteCode) {
        FamilyUser familyUser = this.baseMapper.selectByInviteCode(shopId, inviteCode);
        return ConverterUtil.convert(familyUser, FamilyUserDTO.class);
    }

    @Override
    public FamilyUserDTO selectByInviteByCode(Long shopId, String inviteCode) {
        FamilyUser familyUser = this.baseMapper.selectByInviteByCode(shopId, inviteCode);
        return ConverterUtil.convert(familyUser, FamilyUserDTO.class);
    }

    @Override
    public FamilyUserDTO selectMyInviter(Long shopId, Long userId) {
        FamilyUserQry familyUserQry = new FamilyUserQry();
        familyUserQry.setShopId(shopId);
        familyUserQry.setUserId(userId);
        FamilyUserDTO user = this.selectOne(familyUserQry);
        if(user == null){
            log.error("指定用户不存在 {} {}", shopId, userId);
            throw new RuntimeException("指定用户不存在");
        }
        Long invitedBy = user.getInvitedBy();
        FamilyUserQry invitedByQry = new FamilyUserQry();
        invitedByQry.setShopId(shopId);
        invitedByQry.setUserId(invitedBy);
        FamilyUserDTO invitedByDTO = this.selectOne(invitedByQry);
        return invitedByDTO;
    }

    @Override
    public FamilyUserDTO selectFamilyUserByPhone(Long shopId, String phone) {
        FamilyUserQry familyUserQry = new FamilyUserQry();
        familyUserQry.setShopId(shopId);
        familyUserQry.setPhone(phone);

        FamilyUserDTO familyUserDTO = this.selectOne(familyUserQry);
        if(familyUserDTO == null){
            return null;
        }
        return familyUserDTO;
    }
    public FamilyUserDTO selectFamilyUserByPhoneLike(Long shopId, String phone) {
        LambdaQueryWrapper<FamilyUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FamilyUser::getShopId, shopId);
        wrapper.like(FamilyUser::getPhone, phone);
        FamilyUser familyUser = this.baseMapper.selectOne(wrapper);
        return ConverterUtil.convert(familyUser, FamilyUserDTO.class);
    }

    @Override
    public FamilyUserDTO selectRandom(Long shopId) {
        FamilyUserQry familyUserQry = new FamilyUserQry();
        familyUserQry.setShopId(shopId);
        Long userCount = count(familyUserQry);
        if(userCount == 0){
            return null;
        }else{
            int limit = RandomUtils.getRandomNumberLessThan(userCount.intValue());
            LambdaQueryWrapper<FamilyUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FamilyUser::getShopId, shopId);
            wrapper.last("LIMIT " + limit + ", 1");
            FamilyUser familyUser = this.baseMapper.selectOne(wrapper);
            return ConverterUtil.convert(familyUser, FamilyUserDTO.class);
        }
    }

    @Override
    public String selectAvatarUrl(Long shopId, Long userId) {
        FamilyUserDTO dto = selectFamilyUser(shopId, userId);
        if(dto == null){
            return "";
        }
        return dto.getHeadImg();
    }

    @Override
    public Boolean hasPaid(Long shopId, Long userId) {
        FamilyUserDTO dto = selectFamilyUser(shopId, userId);
        if(dto == null){
            throw new RuntimeException("用户不存在");
        }
        Integer paidTag = dto.getPaidTag();
        return Integer.valueOf(1).equals(paidTag);
    }

    @Override
    public Long countByActivityId(Long shopId, Long activityId, Integer activityType) {
        return countByActivityIdAndTime(shopId, activityId, activityType, null, null);
    }

    @Override
    public Long countByActivityIdAndTime(Long shopId, Long activityId, Integer activityType, Long startTime, Long endTime) {
        LambdaQueryWrapper<FamilyUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FamilyUser::getShopId, shopId);
        wrapper.eq(FamilyUser::getActivityId, activityId);
        wrapper.eq(FamilyUser::getActivityType, activityType);

        // 添加时间范围查询条件
        if (startTime != null) {
            wrapper.ge(FamilyUser::getCreatedTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(FamilyUser::getCreatedTime, endTime);
        }

        Long count = this.baseMapper.selectCount(wrapper);
        if(count == null){
            return 0L;
        }
        return count;
    }

    @Override
    public boolean checkByActivityIdAndUserId(Long shopId, Long userId, Long activityId, Integer activityType) {
        return checkByActivityIdAndUserId(shopId, userId, activityId, null, activityType);
    }

    @Override
    public boolean checkByActivityIdAndUserId(Long shopId, Long userId, Long activityId, Long groupId, Integer activityType) {
        log.info("新客校验 店铺ID={},用户ID={},活动ID={},团ID={},活动类型={}", shopId, userId, activityId, groupId, activityType);
        Assert.notNull(shopId, "shopId 不能为空");
        Assert.notNull(userId, "userId 不能为空");

        LambdaQueryWrapper<FamilyUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FamilyUser::getShopId, shopId);
        wrapper.eq(FamilyUser::getUserId, userId);
        if(groupId != null){
            wrapper.eq(FamilyUser::getGroupId, groupId);
        }

        FamilyUser familyUser = this.baseMapper.selectOne(wrapper);
        if(familyUser == null){
            return Boolean.FALSE;
        }
        if(Objects.equals(activityId, familyUser.getActivityId())){
            if(activityType == Math.toIntExact(familyUser.getActivityType())){
                return true;
            }else{
                log.info("用户 {} 活动类型不匹配 {}", userId, activityType);
            }
        }
        return false;
    }

    @Override
    public void markAsPaid(Long shopId, Long userId) {
        FamilyUserDTO dto = selectFamilyUser(shopId, userId);
        if(dto == null){
            throw new RuntimeException("用户不存在");
        }
        FamilyUserDTO update = new FamilyUserDTO();
        update.setId(dto.getId());
        update.setPaidTag(1);
        update(update);
    }

    @Override
    public void refreshLastLoginTime(Long id, Long lastLoginTime) {
        FamilyUserDTO update = new FamilyUserDTO();
        update.setId(id);
        update.setLastLoginTime(lastLoginTime);
        update(update);
    }

}
