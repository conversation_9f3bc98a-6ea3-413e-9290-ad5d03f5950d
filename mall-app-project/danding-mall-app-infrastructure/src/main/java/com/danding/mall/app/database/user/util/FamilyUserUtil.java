package com.danding.mall.app.database.user.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.mall.app.database.user.entity.FamilyUser;
import com.danding.mall.app.database.user.entity.FamilyUserCustomer;
import com.danding.mall.app.user.dto.FamilyUserQry;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
* <p>
    * 
    * </p>
*
* <AUTHOR>
* @since 2025-02-21
*/
public class FamilyUserUtil{

    public static LambdaQueryWrapper<FamilyUser> getQueryWrapper(FamilyUserQry param, LambdaQueryWrapper<FamilyUser> lambdaQueryWrapper) {
        lambdaQueryWrapper

            //
            .eq(!ObjectUtils.isEmpty(param.getId()), FamilyUser::getId, param.getId())
            //商家ID
            .eq(!ObjectUtils.isEmpty(param.getShopId()), FamilyUser::getShopId, param.getShopId())
            .eq(!ObjectUtils.isEmpty(param.getNickName()), FamilyUser::getNickName, param.getNickName())
            //关联用户ID
            .eq(!ObjectUtils.isEmpty(param.getUserId()), FamilyUser::getUserId, param.getUserId())
            // 用户昵称
            .like(!ObjectUtils.isEmpty(param.getNickName()), FamilyUser::getNickName, param.getNickName())
            .like(!ObjectUtils.isEmpty(param.getNickNameLike()), FamilyUser::getNickName, param.getNickNameLike())
            //手机号
            .eq(!ObjectUtils.isEmpty(param.getPhone()), FamilyUser::getPhone, param.getPhone())
            //密码
            .eq(!ObjectUtils.isEmpty(param.getPassword()), FamilyUser::getPassword, param.getPassword())
            //注册类型
            .eq(!ObjectUtils.isEmpty(param.getRegisterType()), FamilyUser::getRegisterType, param.getRegisterType())
            //我的邀请码
            .eq(!ObjectUtils.isEmpty(param.getInviteCode()), FamilyUser::getInviteCode, param.getInviteCode())
            //注册时支付的金额
            .eq(!ObjectUtils.isEmpty(param.getRegistrationFee()), FamilyUser::getRegistrationFee, param.getRegistrationFee())
            //邀请人
            .eq(!ObjectUtils.isEmpty(param.getInvitedBy()), FamilyUser::getInvitedBy, param.getInvitedBy())
            //邀请人使用的邀请码
            .eq(!ObjectUtils.isEmpty(param.getInvitedByCode()), FamilyUser::getInvitedByCode, param.getInvitedByCode())
            //邀请人的外部用户标识
            .eq(!ObjectUtils.isEmpty(param.getInvitedByOutId()), FamilyUser::getInvitedByOutId, param.getInvitedByOutId())
            //创建人
            .eq(!ObjectUtils.isEmpty(param.getCreatedBy()), FamilyUser::getCreatedBy, param.getCreatedBy())
            //创建时间 (时间戳)
            .eq(!ObjectUtils.isEmpty(param.getCreatedTime()), FamilyUser::getCreatedTime, param.getCreatedTime())
            //更新人
            .eq(!ObjectUtils.isEmpty(param.getUpdatedBy()), FamilyUser::getUpdatedBy, param.getUpdatedBy())
            //更新时间 (时间戳)
            .eq(!ObjectUtils.isEmpty(param.getUpdatedTime()), FamilyUser::getUpdatedTime, param.getUpdatedTime())
            //外部用户标识
            .eq(!ObjectUtils.isEmpty(param.getOutUserId()), FamilyUser::getOutUserId, param.getOutUserId())
            //已邀请用户数量
            .eq(!ObjectUtils.isEmpty(param.getInvitedCount()), FamilyUser::getInvitedCount, param.getInvitedCount())
            .eq(!ObjectUtils.isEmpty(param.getPaidTag()), FamilyUser::getPaidTag, param.getPaidTag())
            .like(!ObjectUtils.isEmpty(param.getOutGroupIds()), FamilyUser::getOutGroupIds, param.getOutGroupIds())
            .like(!ObjectUtils.isEmpty(param.getOutUserIdLike()), FamilyUser::getOutUserId, param.getOutUserIdLike())
            .like(!ObjectUtils.isEmpty(param.getPhoneLike()), FamilyUser::getPhone, param.getPhoneLike())
            /**
             * 原符号       <       <=      >       >=      <>
             * 对应函数    lt()     le()    gt()    ge()    ne()
             */
            .ge(!ObjectUtils.isEmpty(param.getCreatedTimeStart()), FamilyUser::getCreatedTime, param.getCreatedTimeStart())
            .le(!ObjectUtils.isEmpty(param.getCreatedTimeEnd()), FamilyUser::getCreatedTime, param.getCreatedTimeEnd())
            .in(!ObjectUtils.isEmpty(param.getUserIds()), FamilyUser::getUserId, param.getUserIds())
            .in(!ObjectUtils.isEmpty(param.getUserIdsInCustomer()), FamilyUser::getUserId, param.getUserIdsInCustomer());
        if(!ObjectUtils.isEmpty(param.getPhoneOrUserId())){
                //如果phoneOrUserId不为空，则查询phone或userId包含该值的记录
                lambdaQueryWrapper.and(wrapper ->
                        wrapper.eq(FamilyUser::getPhone, param.getPhoneOrUserId())
                                .or()
                                .eq(FamilyUser::getUserId, Long.parseLong(param.getPhoneOrUserId()))
                );
            }
        return lambdaQueryWrapper;
    }
}