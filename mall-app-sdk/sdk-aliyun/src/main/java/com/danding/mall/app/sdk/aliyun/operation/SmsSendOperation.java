package com.danding.mall.app.sdk.aliyun.operation;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class SmsSendOperation {

    @Value("${msg.aliyun.appKey}")
    private String aliKey;
    @Value("${msg.aliyun.appSecret}")
    private String aliSecret;

    private Client client;


    public void sendSms(String phone,
                        String signName,
                        String templateCode,
                        Map<String, Serializable> templateValue) throws Exception {
        Client client = getClient();

//        String signName = smsTemplate.getSignName();
//        String templateCode = smsTemplate.getTemplateCode();

        // 构造请求对象，请填入请求参数值
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam(JSONObject.toJSONString(templateValue));

        // 获取响应对象
        log.info("阿里云短信发送 REQ {}", JSONObject.toJSONString(sendSmsRequest));
        SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
        log.info("阿里云短信发送 RES {}", JSONObject.toJSONString(sendSmsResponse));
        if(!isSuccess(sendSmsResponse)){
            // TODO 短信验证码临时调整
            log.error("短信验证码发送失败");
//            throw new RuntimeException("短信验证码发送失败");
        }
    }

    private boolean isSuccess(SendSmsResponse sendSmsResponse) {
        return sendSmsResponse.statusCode == 200
                && sendSmsResponse.getBody() != null
                && "OK".equals(sendSmsResponse.getBody().getCode());
    }

    private synchronized Client getClient() {
        if(client == null){
            client = createClient();
        }
        return client;
    }

    public Client createClient() {
        Config config = new Config()
                .setAccessKeyId(aliKey)
                .setAccessKeySecret(aliSecret);
        // 配置 Endpoint
        config.endpoint = "dysmsapi.aliyuncs.com";
        try {
            return new Client(config);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {
        // 初始化请求客户端
        Config config = new Config()
                // 配置 AccessKey ID，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId("LTAI5tFqu9xuY5WzSfjFz8Ff")
                // 配置 AccessKey Secret，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret("******************************");

        // 配置 Endpoint
        config.endpoint = "dysmsapi.aliyuncs.com";

        Client client = new Client(config);

        Map<String, Serializable> map = new HashMap<>();
        map.put("code", "3654");
//        map.put("host", Optional.ofNullable("***********").orElse("yang800"));
//        map.put("shopId", Optional.ofNullable(179L).orElse(0L));

        // 构造请求对象，请填入请求参数值
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers("17600195127")
                .setSignName("杭州但丁云科技")
                .setTemplateCode("SMS_484015176")
                .setTemplateParam(JSONObject.toJSONString(map));

        // 获取响应对象
        SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);

        // 响应包含服务端响应的 body 和 headers
        System.out.println(Common.toJSONString(sendSmsResponse));
    }

}
