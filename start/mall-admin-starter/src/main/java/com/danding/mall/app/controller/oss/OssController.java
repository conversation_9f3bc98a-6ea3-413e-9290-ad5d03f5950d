package com.danding.mall.app.controller.oss;

import com.danding.mall.app.base.controller.BaseOssController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * OSS控制器 - 管理端
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
@Api(tags = "OSS文件上传接口 - 管理端")
public class OssController extends BaseOssController {

    @Override
    @ApiOperation("获取STS临时凭证")
    public com.danding.core.client.Result<com.danding.mall.app.base.vo.OssStsTokenVO> getStsToken() {
        return super.getStsToken();
    }

    @Override
    @ApiOperation("获取PostObject上传签名")
    public com.danding.core.client.Result<com.danding.mall.app.base.vo.OssPostSignatureVO> getPostSignature() {
        return super.getPostSignature();
    }

    @Override
    @ApiOperation("生成唯一文件名")
    public com.danding.core.client.Result<String> generateFileName(String originalFilename, String subDir) {
        return super.generateFileName(originalFilename, subDir);
    }
}
